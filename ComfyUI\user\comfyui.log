## ComfyUI-Manager: installing dependencies done.
[2025-08-31 20:42:52.745] ** ComfyUI startup time: 2025-08-31 20:42:52.745
[2025-08-31 20:42:52.745] ** Platform: Windows
[2025-08-31 20:42:52.746] ** Python version: 3.13.6 (tags/v3.13.6:4e66535, Aug  6 2025, 14:36:00) [MSC v.1944 64 bit (AMD64)]
[2025-08-31 20:42:52.746] ** Python executable: C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\python.exe
[2025-08-31 20:42:52.747] ** ComfyUI Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 20:42:52.747] ** ComfyUI Base Folder Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 20:42:52.747] ** User directory: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user
[2025-08-31 20:42:52.756] ** ComfyUI-Manager config path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-08-31 20:42:52.757] ** Log path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-08-31 20:42:53.622]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\rgthree-comfy
[2025-08-31 20:42:53.622]    1.9 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Manager-main
[2025-08-31 20:42:53.622] 
[2025-08-31 20:42:55.474] Checkpoint files will always be loaded safely.
