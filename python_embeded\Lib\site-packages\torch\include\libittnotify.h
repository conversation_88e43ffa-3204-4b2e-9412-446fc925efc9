/*
  Copyright (C) 2005-2019 Intel Corporation

  SPDX-License-Identifier: GPL-2.0-only OR BSD-3-Clause
*/

#ifndef _LIBITTNOTIFY_H_
#define _LIBITTNOTIFY_H_

#ifndef __ITT_INTERNAL_INCLUDE
#  if defined WIN32 || defined _WIN32
#    pragma message("WARNING!!! Include file libittnotify.h is deprecated and should not be included anymore")
#  else /* WIN32 */
#    warning "Include file libittnotify.h is deprecated and should not be included anymore"
#  endif /* WIN32 */
#endif /* __ITT_INTERNAL_INCLUDE */
#include "legacy/ittnotify.h"

#endif /* _LIBITTNOTIFY_H_ */
