--- a/py/iopaint/helper.py
+++ b/py/iopaint/helper.py
@@ -1,5 +1,5 @@
 import base64
-import imghdr
+import filetype
 import io
 import os
 import sys
@@ -301,9 +301,12 @@ def is_mac():
 
 
 def get_image_ext(img_bytes):
-    w = imghdr.what("", img_bytes)
-    if w is None:
+    # Use filetype instead of deprecated imghdr (removed in Python 3.13)
+    kind = filetype.guess(img_bytes)
+    if kind is None:
         w = "jpeg"
+    else:
+        w = kind.extension
     return w
 
 
