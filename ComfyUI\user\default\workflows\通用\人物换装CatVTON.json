{"id": "6fd9edfc-ec1d-495b-a75b-5e60c844f657", "revision": 0, "last_node_id": 18, "last_link_id": 24, "nodes": [{"id": 13, "type": "LayerMask: HumanPartsUltra", "pos": [700, 300], "size": [352.79998779296875, 534], "flags": {}, "order": 2, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 16}, {"localized_name": "face", "name": "face", "type": "BOOLEAN", "widget": {"name": "face"}, "link": null}, {"localized_name": "hair", "name": "hair", "type": "BOOLEAN", "widget": {"name": "hair"}, "link": null}, {"localized_name": "glasses", "name": "glasses", "type": "BOOLEAN", "widget": {"name": "glasses"}, "link": null}, {"localized_name": "top_clothes", "name": "top_clothes", "type": "BOOLEAN", "widget": {"name": "top_clothes"}, "link": null}, {"localized_name": "bottom_clothes", "name": "bottom_clothes", "type": "BOOLEAN", "widget": {"name": "bottom_clothes"}, "link": null}, {"localized_name": "torso_skin", "name": "torso_skin", "type": "BOOLEAN", "widget": {"name": "torso_skin"}, "link": null}, {"localized_name": "left_arm", "name": "left_arm", "type": "BOOLEAN", "widget": {"name": "left_arm"}, "link": null}, {"localized_name": "right_arm", "name": "right_arm", "type": "BOOLEAN", "widget": {"name": "right_arm"}, "link": null}, {"localized_name": "left_leg", "name": "left_leg", "type": "BOOLEAN", "widget": {"name": "left_leg"}, "link": null}, {"localized_name": "right_leg", "name": "right_leg", "type": "BOOLEAN", "widget": {"name": "right_leg"}, "link": null}, {"localized_name": "left_foot", "name": "left_foot", "type": "BOOLEAN", "widget": {"name": "left_foot"}, "link": null}, {"localized_name": "right_foot", "name": "right_foot", "type": "BOOLEAN", "widget": {"name": "right_foot"}, "link": null}, {"localized_name": "detail_method", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "detail_erode", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "detail_dilate", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "black_point", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "white_point", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "process_detail", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "device", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "max_megapixels", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 1, "links": [17, 18]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerMask: HumanPartsUltra"}, "widgets_values": [false, false, false, true, false, false, false, false, false, false, false, false, "VITMatte", 8, 6, 0.01, 0.99, true, "cuda", 2], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 11, "type": "LayerMask: MaskPreview", "pos": [1150, 500], "size": [323.2476806640625, 354.96905517578125], "flags": {}, "order": 3, "mode": 0, "inputs": [{"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 17}], "outputs": [], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerMask: MaskPreview"}, "widgets_values": [], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 1, "type": "CatVTONWrapper", "pos": [1150, 190], "size": [315, 218], "flags": {}, "order": 4, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 1}, {"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 18}, {"label": "refer_image", "localized_name": "refer_image", "name": "refer_image", "type": "IMAGE", "link": 7}, {"localized_name": "mask_grow", "name": "mask_grow", "type": "INT", "widget": {"name": "mask_grow"}, "link": null}, {"localized_name": "mixed_precision", "name": "mixed_precision", "type": "COMBO", "widget": {"name": "mixed_precision"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [24]}], "properties": {"cnr_id": "ComfyUI_CatVTON_Wrapper", "ver": "e2ae9b62c53a7c2e009f2448975c6909c83f73b5", "Node name for S&R": "CatVTONWrapper"}, "widgets_values": [25, "fp16", 358516013397608, "randomize", 50, 3]}, {"id": 18, "type": "SaveImage", "pos": [1580, 180], "size": [520, 720], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "images", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 24}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29"}, "widgets_values": ["ComfyUI"]}, {"id": 2, "type": "LoadImage", "pos": [230, 190], "size": [315, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [1, 16]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadImage"}, "widgets_values": ["kidsmodel.jpg", "image"]}, {"id": 7, "type": "LoadImage", "pos": [216.7161407470703, 608.8632202148438], "size": [315, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [7]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadImage"}, "widgets_values": ["OIP.webp", "image"]}], "links": [[1, 2, 0, 1, 0, "IMAGE"], [7, 7, 0, 1, 2, "IMAGE"], [16, 2, 0, 13, 0, "IMAGE"], [17, 13, 1, 11, 0, "MASK"], [18, 13, 1, 1, 1, "MASK"], [24, 1, 0, 18, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.620921323059155, "offset": [592.9432062961716, 205.73750103400684]}, "frontendVersion": "1.17.11", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}