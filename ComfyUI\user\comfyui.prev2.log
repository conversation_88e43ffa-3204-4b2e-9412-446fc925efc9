## ComfyUI-Manager: installing dependencies done.
[2025-08-31 19:44:19.487] ** ComfyUI startup time: 2025-08-31 19:44:19.487
[2025-08-31 19:44:19.488] ** Platform: Windows
[2025-08-31 19:44:19.489] ** Python version: 3.13.6 (tags/v3.13.6:4e66535, Aug  6 2025, 14:36:00) [MSC v.1944 64 bit (AMD64)]
[2025-08-31 19:44:19.490] ** Python executable: C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\python.exe
[2025-08-31 19:44:19.490] ** ComfyUI Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 19:44:19.491] ** ComfyUI Base Folder Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 19:44:19.492] ** User directory: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user
[2025-08-31 19:44:19.493] ** ComfyUI-Manager config path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-08-31 19:44:19.494] ** Log path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-08-31 19:44:20.423]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\rgthree-comfy
[2025-08-31 19:44:20.424]    2.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Manager-main
[2025-08-31 19:44:20.424] 
[2025-08-31 19:44:24.479] Checkpoint files will always be loaded safely.
[2025-08-31 19:44:26.960] Total VRAM 8151 MB, total RAM 32472 MB
[2025-08-31 19:44:26.960] pytorch version: 2.8.0+cu129
[2025-08-31 19:44:26.961] Set vram state to: NORMAL_VRAM
[2025-08-31 19:44:26.961] Device: cuda:0 NVIDIA GeForce RTX 5060 Laptop GPU : cudaMallocAsync
[2025-08-31 19:44:30.792] Using pytorch attention
[2025-08-31 19:44:39.004] Python version: 3.13.6 (tags/v3.13.6:4e66535, Aug  6 2025, 14:36:00) [MSC v.1944 64 bit (AMD64)]
[2025-08-31 19:44:39.005] ComfyUI version: 0.3.56
[2025-08-31 19:44:39.177] ComfyUI frontend version: 1.25.11
[2025-08-31 19:44:39.183] [Prompt Server] web root: C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-08-31 19:44:44.721] [Crystools [0;32mINFO[0m] Crystools version: 1.27.3
[2025-08-31 19:44:44.864] [Crystools [0;32mINFO[0m] Platform release: 11
[2025-08-31 19:44:44.864] [Crystools [0;32mINFO[0m] JETSON: Not detected.
[2025-08-31 19:44:44.866] [Crystools [0;32mINFO[0m] CPU: Intel(R) Core(TM) i7-14650HX - Arch: AMD64 - OS: Windows 11
[2025-08-31 19:44:44.873] [Crystools [0;32mINFO[0m] pynvml (NVIDIA) initialized.
[2025-08-31 19:44:44.874] [Crystools [0;32mINFO[0m] GPU/s:
[2025-08-31 19:44:44.887] [Crystools [0;32mINFO[0m] 0) NVIDIA GeForce RTX 5060 Laptop GPU
[2025-08-31 19:44:44.888] [Crystools [0;32mINFO[0m] NVIDIA Driver: 573.01
[2025-08-31 19:44:44.904] Total VRAM 8151 MB, total RAM 32472 MB
[2025-08-31 19:44:44.904] pytorch version: 2.8.0+cu129
[2025-08-31 19:44:44.905] Set vram state to: NORMAL_VRAM
[2025-08-31 19:44:44.905] Device: cuda:0 NVIDIA GeForce RTX 5060 Laptop GPU : cudaMallocAsync
[2025-08-31 19:44:45.003] ### Loading: ComfyUI-Manager (V3.36)
[2025-08-31 19:44:45.003] [ComfyUI-Manager] network_mode: public
[2025-08-31 19:44:45.069] ### ComfyUI Revision: 150 [4449e147] *DETACHED | Released on '2025-08-30'
[2025-08-31 19:44:45.833] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-08-31 19:44:46.323] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-08-31 19:44:46.337] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-08-31 19:44:46.516] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-08-31 19:44:46.778] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-08-31 19:44:53.721] FETCH ComfyRegistry Data: 5/96
[2025-08-31 19:44:56.801] ------------------------------------------
[2025-08-31 19:44:56.802] Comfyroll Studio v1.76 :  175 Nodes Loaded
[2025-08-31 19:44:56.803] ------------------------------------------
[2025-08-31 19:44:56.804] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-08-31 19:44:56.805] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-08-31 19:44:56.806] ------------------------------------------
[2025-08-31 19:44:56.839] [C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts
[2025-08-31 19:44:56.844] [C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False
[2025-08-31 19:44:56.849] [C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider']
[2025-08-31 19:44:57.207] # 😺dzNodes: LayerStyle -> Cannot import name 'guidedFilter' from 'cv2.ximgproc'
A few nodes cannot works properly, while most nodes are not affected. Please REINSTALL package 'opencv-contrib-python'.
For detail refer to https://github.com/chflame163/ComfyUI_LayerStyle/issues/5
[2025-08-31 19:45:02.981] FETCH ComfyRegistry Data: 10/96
[2025-08-31 19:45:03.032] C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\timm\models\layers\__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
  warnings.warn(f"Importing from {__name__} is deprecated, please import via timm.layers", FutureWarning)
[2025-08-31 19:45:04.162] C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-08-31 19:45:04.667] # 😺dzNodes: WordCloud ->  find 345 fonts in C:\Windows\fonts
[2025-08-31 19:45:06.336] 
[2025-08-31 19:45:06.337] [rgthree-comfy] Loaded 48 fantastic nodes. 🎉
[2025-08-31 19:45:06.337] 
[2025-08-31 19:45:08.130] WAS Node Suite: Created default conf file at `C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-node-suite-comfyui\was_suite_config.json`.
[2025-08-31 19:45:08.689] WAS Node Suite: OpenCV Python FFMPEG support is enabled
[2025-08-31 19:45:08.691] WAS Node Suite Warning: `ffmpeg_bin_path` is not set in `C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-node-suite-comfyui\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.
[2025-08-31 19:45:09.073] WAS Node Suite: Finished. Loaded 220 nodes successfully.
[2025-08-31 19:45:09.075] 
	"The only limit to our realization of tomorrow will be our doubts of today." - Franklin D. Roosevelt
[2025-08-31 19:45:09.076] 
[2025-08-31 19:45:09.088] 
Import times for custom nodes:
[2025-08-31 19:45:09.088]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-08-31 19:45:09.088]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_AdvancedRefluxControl
[2025-08-31 19:45:09.089]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_essentials
[2025-08-31 19:45:09.089]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-08-31 19:45:09.089]    0.1 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-ic-light
[2025-08-31 19:45:09.089]    0.1 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Manager-main
[2025-08-31 19:45:09.089]    0.3 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-08-31 19:45:09.090]    0.8 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Crystools
[2025-08-31 19:45:09.090]    0.8 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_wordcloud
[2025-08-31 19:45:09.090]    1.4 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-08-31 19:45:09.090]    1.7 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\rgthree-comfy
[2025-08-31 19:45:09.090]    2.7 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-node-suite-comfyui
[2025-08-31 19:45:09.091]    6.7 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle
[2025-08-31 19:45:09.091]   10.3 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_CatVTON_Wrapper
[2025-08-31 19:45:09.091] 
[2025-08-31 19:45:10.360] Context impl SQLiteImpl.
[2025-08-31 19:45:10.361] Will assume non-transactional DDL.
[2025-08-31 19:45:10.365] No target revision found.
[2025-08-31 19:45:10.444] Starting server

[2025-08-31 19:45:10.446] To see the GUI go to: http://127.0.0.1:8188
[2025-08-31 19:45:12.048] FETCH ComfyRegistry Data: 15/96
[2025-08-31 19:45:19.539] FETCH ComfyRegistry Data: 20/96
[2025-08-31 19:45:27.436] FETCH ComfyRegistry Data: 25/96
[2025-08-31 19:45:35.399] FETCH ComfyRegistry Data: 30/96
[2025-08-31 19:45:41.670] FETCH ComfyRegistry Data: 35/96
[2025-08-31 19:45:47.893] FETCH ComfyRegistry Data: 40/96
[2025-08-31 19:45:54.546] FETCH ComfyRegistry Data: 45/96
[2025-08-31 19:46:01.342] FETCH ComfyRegistry Data: 50/96
[2025-08-31 19:46:06.446] got prompt
[2025-08-31 19:46:09.144] FETCH ComfyRegistry Data: 55/96
[2025-08-31 19:46:21.230] FETCH ComfyRegistry Data: 60/96
[2025-08-31 19:46:26.546] final text_encoder_type: bert-base-uncased
[2025-08-31 19:46:28.700] FETCH ComfyRegistry Data: 65/96
[2025-08-31 19:46:33.923] C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\transformers\modeling_utils.py:1773: FutureWarning: The `device` argument is deprecated and will be removed in v5 of Transformers.
  warnings.warn(
[2025-08-31 19:46:36.234] FETCH ComfyRegistry Data: 70/96
[2025-08-31 19:46:37.258] C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\local_groundingdino\models\GroundingDINO\transformer.py:862: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with torch.cuda.amp.autocast(enabled=False):
[2025-08-31 19:46:43.672] FETCH ComfyRegistry Data: 75/96
[2025-08-31 19:46:47.072] # 😺dzNodes: LayerStyle -> SegmentAnythingUltra V2 Processed 1 image(s).
[2025-08-31 19:46:47.194] # 😺dzNodes: LayerStyle -> lama copy
[2025-08-31 19:46:47.227] # 😺dzNodes: LayerStyle -> config file written: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\temp\_lama_m7mnh1paks7gve9e_temp\config.json
[2025-08-31 19:46:50.087] !!! Exception during processing !!! No module named 'imghdr'
[2025-08-31 19:46:50.107] Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 496, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 315, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 289, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 277, in process_inputs
    result = f(**inputs)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\lama.py", line 111, in lama
    cli.run(model=lama_model, device=device, image=Path(image_dir), mask=Path(mask_dir), output=Path(result_dir), config=Path(config_dir))
    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\cli.py", line 81, in run
    from .download import cli_download_model, scan_models
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\download.py", line 18, in <module>
    from .model.original_sd_configs import get_config_files
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\__init__.py", line 1, in <module>
    from .anytext.anytext_model import AnyText
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\anytext_model.py", line 5, in <module>
    from ...model.anytext.anytext_pipeline import AnyTextPipeline
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\anytext_pipeline.py", line 20, in <module>
    from ...model.anytext.cldm.model import create_model, load_state_dict
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\cldm\model.py", line 5, in <module>
    from iopaint.model.anytext.ldm.util import instantiate_from_config
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\__init__.py", line 1, in <module>
    from .anytext.anytext_model import AnyText
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\anytext_model.py", line 6, in <module>
    from ...model.base import DiffusionInpaintModel
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\base.py", line 9, in <module>
    from ..helper import (
    ...<4 lines>...
    )
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\helper.py", line 2, in <module>
    import imghdr
ModuleNotFoundError: No module named 'imghdr'

[2025-08-31 19:46:50.120] Prompt executed in 43.67 seconds
[2025-08-31 19:46:51.534] FETCH ComfyRegistry Data: 80/96
[2025-08-31 19:46:58.454] FETCH ComfyRegistry Data: 85/96
[2025-08-31 19:47:04.974] FETCH ComfyRegistry Data: 90/96
[2025-08-31 19:47:08.254] [ComfyUI-Manager] The ComfyRegistry cache update is still in progress, so an outdated cache is being used.
[2025-08-31 19:47:08.481] FETCH DATA from: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\cache\1514988643_custom-node-list.json [DONE]
[2025-08-31 19:47:08.811] FETCH DATA from: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\cache\746607195_github-stats.json [DONE]
[2025-08-31 19:47:08.863] FETCH DATA from: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\cache\832903789_extras.json [DONE]
[2025-08-31 19:47:09.112] FETCH DATA from: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\cache\1742899825_extension-node-map.json [DONE]
[2025-08-31 19:47:11.861] FETCH ComfyRegistry Data: 95/96
[2025-08-31 19:47:13.538] FETCH ComfyRegistry Data [DONE]
[2025-08-31 19:47:14.211] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-08-31 19:47:14.315] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json[ComfyUI-Manager] The ComfyRegistry cache update is still in progress, so an outdated cache is being used.
[2025-08-31 19:47:14.946] FETCH DATA from: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\cache\1514988643_custom-node-list.json [DONE]
[2025-08-31 19:47:15.210] FETCH DATA from: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\cache\746607195_github-stats.json [DONE]
[2025-08-31 19:47:15.236] FETCH DATA from: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\cache\832903789_extras.json [DONE]
[2025-08-31 19:47:15.244]  [DONE]
[2025-08-31 19:47:15.267] Start fetching...
Fetching: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
Fetching: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\rgthree-comfy
Fetching: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_CatVTON_Wrapper
Fetching: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_AdvancedRefluxControl[2025-08-31 19:47:15.406] [ComfyUI-Manager] All startup tasks have been completed.

Fetching done.
[2025-08-31 19:47:17.060] FETCH DATA from: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\cache\1742899825_extension-node-map.json [DONE]
[2025-08-31 19:47:50.001] got prompt
[2025-08-31 19:47:50.039] # 😺dzNodes: LayerStyle -> lama copy
[2025-08-31 19:47:50.123] # 😺dzNodes: LayerStyle -> config file written: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\temp\_lama_0n0o0km9lmbbbk1v_temp\config.json
[2025-08-31 19:47:51.973] !!! Exception during processing !!! No module named 'imghdr'
[2025-08-31 19:47:51.980] Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 496, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 315, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 289, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 277, in process_inputs
    result = f(**inputs)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\lama.py", line 111, in lama
    cli.run(model=lama_model, device=device, image=Path(image_dir), mask=Path(mask_dir), output=Path(result_dir), config=Path(config_dir))
    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\cli.py", line 81, in run
    from .download import cli_download_model, scan_models
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\download.py", line 18, in <module>
    from .model.original_sd_configs import get_config_files
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\__init__.py", line 1, in <module>
    from .anytext.anytext_model import AnyText
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\anytext_model.py", line 6, in <module>
    from ...model.base import DiffusionInpaintModel
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\base.py", line 9, in <module>
    from ..helper import (
    ...<4 lines>...
    )
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\helper.py", line 2, in <module>
    import imghdr
ModuleNotFoundError: No module named 'imghdr'

[2025-08-31 19:47:51.993] Prompt executed in 1.99 seconds
[2025-08-31 19:47:59.764] got prompt
[2025-08-31 19:47:59.783] # 😺dzNodes: LayerStyle -> lama copy
[2025-08-31 19:47:59.816] # 😺dzNodes: LayerStyle -> config file written: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\temp\_lama_m4ixhyvproav6fym_temp\config.json
[2025-08-31 19:48:01.514] !!! Exception during processing !!! No module named 'imghdr'
[2025-08-31 19:48:01.520] Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 496, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 315, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 289, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 277, in process_inputs
    result = f(**inputs)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\lama.py", line 111, in lama
    cli.run(model=lama_model, device=device, image=Path(image_dir), mask=Path(mask_dir), output=Path(result_dir), config=Path(config_dir))
    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\cli.py", line 81, in run
    from .download import cli_download_model, scan_models
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\download.py", line 18, in <module>
    from .model.original_sd_configs import get_config_files
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\__init__.py", line 1, in <module>
    from .anytext.anytext_model import AnyText
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\anytext_model.py", line 6, in <module>
    from ...model.base import DiffusionInpaintModel
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\base.py", line 9, in <module>
    from ..helper import (
    ...<4 lines>...
    )
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\helper.py", line 2, in <module>
    import imghdr
ModuleNotFoundError: No module named 'imghdr'

[2025-08-31 19:48:01.530] Prompt executed in 1.76 seconds
[2025-08-31 19:48:05.709] got prompt
[2025-08-31 19:48:05.730] # 😺dzNodes: LayerStyle -> lama copy
[2025-08-31 19:48:05.765] # 😺dzNodes: LayerStyle -> config file written: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\temp\_lama_mdlumdvlpuhgjyeu_temp\config.json
[2025-08-31 19:48:07.470] !!! Exception during processing !!! No module named 'imghdr'
[2025-08-31 19:48:07.476] Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 496, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 315, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 289, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 277, in process_inputs
    result = f(**inputs)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\lama.py", line 111, in lama
    cli.run(model=lama_model, device=device, image=Path(image_dir), mask=Path(mask_dir), output=Path(result_dir), config=Path(config_dir))
    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\cli.py", line 81, in run
    from .download import cli_download_model, scan_models
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\download.py", line 18, in <module>
    from .model.original_sd_configs import get_config_files
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\__init__.py", line 1, in <module>
    from .anytext.anytext_model import AnyText
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\anytext_model.py", line 6, in <module>
    from ...model.base import DiffusionInpaintModel
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\base.py", line 9, in <module>
    from ..helper import (
    ...<4 lines>...
    )
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\helper.py", line 2, in <module>
    import imghdr
ModuleNotFoundError: No module named 'imghdr'

[2025-08-31 19:48:07.486] Prompt executed in 1.77 seconds
[2025-08-31 19:52:44.851] 
Stopped server
