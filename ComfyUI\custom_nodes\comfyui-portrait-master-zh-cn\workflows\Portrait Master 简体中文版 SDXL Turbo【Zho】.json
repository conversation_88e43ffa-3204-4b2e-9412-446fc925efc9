{"last_node_id": 12, "last_link_id": 15, "nodes": [{"id": 7, "type": "CheckpointLoaderSimple", "pos": [-30, 1270], "size": {"0": 340, "1": 100}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [6, 12], "shape": 3, "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [1, 3], "shape": 3, "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [5], "shape": 3, "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sd_xl_turbo_1.0_fp16.safetensors"]}, {"id": 3, "type": "CLIPTextEncode", "pos": [330, 1520], "size": {"0": 310, "1": 75.99998474121094}, "flags": {"collapsed": true}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [8], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 6, "type": "KSamplerSelect", "pos": [330, 1380], "size": {"0": 210, "1": 60}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [9], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler_ancestral"]}, {"id": 8, "type": "SDTurboScheduler", "pos": [330, 1270], "size": {"0": 210, "1": 70}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 12, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [10], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SDTurboScheduler"}, "widgets_values": [1]}, {"id": 1, "type": "EmptyLatentImage", "pos": [-30, 1410], "size": {"0": 340, "1": 110}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [384, 640, 1]}, {"id": 4, "type": "VAEDecode", "pos": [810, 1270], "size": {"0": 140, "1": 50}, "flags": {"collapsed": false}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 4}, {"name": "vae", "type": "VAE", "link": 5, "slot_index": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 5, "type": "SamplerCustom", "pos": [560, 1270], "size": {"0": 240, "1": 250}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 6, "slot_index": 0}, {"name": "positive", "type": "CONDITIONING", "link": 7, "slot_index": 1}, {"name": "negative", "type": "CONDITIONING", "link": 8}, {"name": "sampler", "type": "SAMPLER", "link": 9, "slot_index": 3}, {"name": "sigmas", "type": "SIGMAS", "link": 10, "slot_index": 4}, {"name": "latent_image", "type": "LATENT", "link": 11, "slot_index": 5}], "outputs": [{"name": "output", "type": "LATENT", "links": [4], "shape": 3, "slot_index": 0}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 754734654447989, "randomize", 1]}, {"id": 9, "type": "PreviewImage", "pos": [470, 1558], "size": {"0": 480, "1": 750}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 13}], "title": "SDXL Turbo", "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 2, "type": "CLIPTextEncode", "pos": [330, 1480], "size": {"0": 310, "1": 54}, "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1, "slot_index": 0}, {"name": "text", "type": "STRING", "link": 15, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["delicious eggs milk onion tomato mushrooms cheese salt steak noodles brunch"]}, {"id": 12, "type": "PortraitMaster_中文版", "pos": [-30, 1560], "size": {"0": 490, "1": 752}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "prompt", "type": "STRING", "links": [15], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "PortraitMaster_中文版"}, "widgets_values": ["肩部以上肖像", 1.5, "女性", "法国人", "-", 0.5, "伤心", 1.5, "梨形", 1, 0.2, "不对称剪裁", 1, 20, 0.5, 0.3, 0.56, 0, 0, 0, 0.77, 0.63, 0.92, 0.8, "raw photo, (realistic:1.5)", "(white background:1.5)", ""]}], "links": [[1, 7, 1, 2, 0, "CLIP"], [3, 7, 1, 3, 0, "CLIP"], [4, 5, 0, 4, 0, "LATENT"], [5, 7, 2, 4, 1, "VAE"], [6, 7, 0, 5, 0, "MODEL"], [7, 2, 0, 5, 1, "CONDITIONING"], [8, 3, 0, 5, 2, "CONDITIONING"], [9, 6, 0, 5, 3, "SAMPLER"], [10, 8, 0, 5, 4, "SIGMAS"], [11, 1, 0, 5, 5, "LATENT"], [12, 7, 0, 8, 0, "MODEL"], [13, 4, 0, 9, 0, "IMAGE"], [15, 12, 0, 2, 1, "STRING"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}