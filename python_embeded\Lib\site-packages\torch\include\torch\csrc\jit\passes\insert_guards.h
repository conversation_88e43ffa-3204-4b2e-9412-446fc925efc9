#pragma once

#include <ATen/ATen.h>
#include <ATen/core/ivalue.h>
#include <ATen/core/jit_type.h>
#include <ATen/core/stack.h>
#include <torch/csrc/Export.h>
#include <torch/csrc/jit/ir/ir.h>

#include <list>
#include <vector>

namespace torch::jit {

TORCH_API void InsertGuards(std::shared_ptr<Graph> graph);

TORCH_API void RemoveProfilingNodes(const std::shared_ptr<Graph>& graph);

} // namespace torch::jit
