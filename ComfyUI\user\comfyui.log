## ComfyUI-Manager: installing dependencies done.
[2025-08-31 21:29:40.421] ** ComfyUI startup time: 2025-08-31 21:29:40.421
[2025-08-31 21:29:40.421] ** Platform: Windows
[2025-08-31 21:29:40.421] ** Python version: 3.13.6 (tags/v3.13.6:4e66535, Aug  6 2025, 14:36:00) [MSC v.1944 64 bit (AMD64)]
[2025-08-31 21:29:40.422] ** Python executable: C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\python.exe
[2025-08-31 21:29:40.422] ** ComfyUI Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 21:29:40.422] ** ComfyUI Base Folder Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 21:29:40.422] ** User directory: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user
[2025-08-31 21:29:40.431] ** ComfyUI-Manager config path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-08-31 21:29:40.431] ** Log path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-08-31 21:29:42.415]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\rgthree-comfy
[2025-08-31 21:29:42.415]    3.4 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Manager-main
[2025-08-31 21:29:42.416] 
