{"last_node_id": 98, "last_link_id": 132, "nodes": [{"id": 25, "type": "VideoLinearCFGGuidance", "pos": [-968.6773160171516, 1771.8887333222322], "size": {"0": 220, "1": 60}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 25}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [26], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VideoLinearCFGGuidance"}, "widgets_values": [1]}, {"id": 26, "type": "ImageOnlyCheckpointLoader", "pos": [-1368.6773160171506, 1771.8887333222322], "size": {"0": 369.6000061035156, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [25], "shape": 3, "slot_index": 0}, {"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [27], "shape": 3, "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [29, 36], "shape": 3, "slot_index": 2}], "properties": {"Node name for S&R": "ImageOnlyCheckpointLoader"}, "widgets_values": ["svd.safetensors"]}, {"id": 43, "type": "PreviewImage", "pos": [-1073.6637709417982, 2271.************], "size": {"0": 210, "1": 246}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 49}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 46, "type": "Text_Image_Frame_Zho", "pos": [-15.896734374740626, 2271.0945216146683], "size": {"0": 280, "1": 420}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "image", "type": "IMAGE", "links": [51, 53], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Text_Image_Frame_Zho"}, "widgets_values": ["1", 27, 1, "OverlockSC-Regular", "center", 10, 50, "#ffffff", 0, "blue", 0, 0, 60, 50, false]}, {"id": 47, "type": "PreviewImage", "pos": [284.10326562526365, 2271.0945216146683], "size": {"0": 210, "1": 246}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 53}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-1364, 682], "size": {"0": 310, "1": 100}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [65], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [66], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [67], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["XXMix_9realisticSDXL.safetensors"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [-1360, 820], "size": {"0": 310, "1": 110}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [68], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1280, 768, 6]}, {"id": 56, "type": "Prompts Everywhere", "pos": [-1030, 970], "size": {"0": 180, "1": 50}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "CONDITIONING", "type": "*", "link": 69, "slot_index": 0, "color_on": "#FFA931"}, {"name": "CONDITIONING", "type": "*", "link": 70, "color_on": "#FFA931"}], "properties": {"group_restricted": false, "color_restricted": false, "Node name for S&R": "Prompts Everywhere"}}, {"id": 55, "type": "Anything Everywhere3", "pos": [-1030, 680], "size": {"0": 180, "1": 70}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "MODEL", "type": "*", "link": 65, "color_on": "#B39DDB"}, {"name": "CLIP", "type": "*", "link": 66, "color_on": "#FFD500"}, {"name": "VAE", "type": "*", "link": 67, "color_on": "#FF6E6E"}], "properties": {"group_restricted": false, "color_restricted": false, "Node name for S&R": "Anything Everywhere3"}}, {"id": 54, "type": "Anything Everywhere", "pos": [-1030, 820], "size": {"0": 180, "1": 30}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "LATENT", "type": "*", "link": 68, "color_on": "#FF9CF9"}], "properties": {"group_restricted": false, "color_restricted": false, "Node name for S&R": "Anything Everywhere"}}, {"id": 15, "type": "VAEDecode", "pos": [572.947774211009, 683.5517590383425], "size": {"0": 170, "1": 50}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 16}, {"name": "vae", "type": "VAE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [18, 40, 41], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 41, "type": "Any Switch (rgthree)", "pos": [769.9323884512972, 1438.9850893170105], "size": {"0": 210, "1": 106}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "any_01", "type": "IMAGE", "link": 42, "dir": 3}, {"name": "any_02", "type": "IMAGE", "link": 43, "dir": 3}, {"name": "any_03", "type": "IMAGE", "link": null, "dir": 3}, {"name": "any_04", "type": "IMAGE", "link": null, "dir": 3}, {"name": "any_05", "type": "IMAGE", "link": null, "dir": 3}], "outputs": [{"name": "*", "type": "IMAGE", "links": [131], "shape": 3, "dir": 4, "label": "IMAGE", "slot_index": 0}], "properties": {"Node name for S&R": "Any Switch (rgthree)"}}, {"id": 16, "type": "CLIPTextEncode", "pos": [-1360, 970], "size": {"0": 310, "1": 54}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": null, "slot_index": 0}, {"name": "text", "type": "STRING", "link": null, "widget": {"name": "text"}, "slot_index": 1}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [69], "slot_index": 0}], "title": "P Prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful scenery nature glass bottle landscape, , purple galaxy bottle,"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [-1360, 1060], "size": {"0": 310, "1": 54}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": null}, {"name": "text", "type": "STRING", "link": null, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [70], "slot_index": 0}], "title": "N Prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 64, "type": "Anything Everywhere?", "pos": [-240, 830], "size": {"0": 210, "1": 82}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "STRING", "type": "*", "link": 99, "color_on": ""}], "properties": {"group_restricted": false, "color_restricted": false, "Node name for S&R": "Anything Everywhere?"}, "widgets_values": ["P Prompt", ".*"]}, {"id": 65, "type": "Anything Everywhere?", "pos": [-240, 950], "size": {"0": 210, "1": 82}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "STRING", "type": "*", "link": 84, "color_on": ""}], "properties": {"group_restricted": false, "color_restricted": false, "Node name for S&R": "Anything Everywhere?"}, "widgets_values": ["N Prompt", ".*"]}, {"id": 24, "type": "PromptExpansion", "pos": [-240, 680], "size": {"0": 210, "1": 110}, "flags": {}, "order": 20, "mode": 4, "inputs": [{"name": "text", "type": "STRING", "link": 98, "widget": {"name": "text"}}, {"name": "seed", "type": "INT", "link": null, "widget": {"name": "seed"}}], "outputs": [{"name": "final_prompt", "type": "STRING", "links": [99], "shape": 3, "slot_index": 0}, {"name": "seed", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "PromptExpansion"}, "widgets_values": ["", 38104916, "randomize", "No"]}, {"id": 29, "type": "ImageScale", "pos": [-1368.4984245249907, 1911.8649441447385], "size": {"0": 370, "1": 130}, "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 131, "slot_index": 0}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [33], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 1024, 576, "disabled"]}, {"id": 30, "type": "Seed Everywhere", "pos": [-1360, 1150], "size": {"0": 310, "1": 82}, "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": null, "shape": 3}], "properties": {"group_restricted": false, "color_restricted": false, "Node name for S&R": "Seed Everywhere"}, "widgets_values": [220414632974098, "fixed"]}, {"id": 27, "type": "K<PERSON><PERSON><PERSON>", "pos": [-728.6773160171534, 1851.8887333222322], "size": {"0": 210, "1": 240}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 26}, {"name": "positive", "type": "CONDITIONING", "link": 30}, {"name": "negative", "type": "CONDITIONING", "link": 31}, {"name": "latent_image", "type": "LATENT", "link": 32}, {"name": "seed", "type": "INT", "link": null, "widget": {"name": "seed"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [35], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [770875965688191, "randomize", 20, 2.5, "euler", "karras", 1]}, {"id": 39, "type": "Preview Chooser", "pos": [771.4823346373424, 680.6379678767976], "size": {"0": 410, "1": 510}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 40}, {"name": "latents", "type": "LATENT", "link": 81}], "outputs": [{"name": "images", "type": "IMAGE", "links": [42], "shape": 3, "slot_index": 0}, {"name": "latent", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "Preview Chooser"}, "widgets_values": ["Only pause if batch", 1, "", ""]}, {"id": 40, "type": "ImageBatchGet", "pos": [769.2539445840604, 1283.642364369948], "size": [210, 60], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 41}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [43], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageBatchGet"}, "widgets_values": [4]}, {"id": 31, "type": "VAEDecode", "pos": [-498.67731601715536, 1851.8887333222322], "size": {"0": 140, "1": 50}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 35}, {"name": "vae", "type": "VAE", "link": 36, "slot_index": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [38, 47, 123], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 14, "type": "K<PERSON><PERSON><PERSON>", "pos": [292.9477742110095, 683.5517590383425], "size": {"0": 270, "1": 262}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 124}, {"name": "positive", "type": "CONDITIONING", "link": null}, {"name": "negative", "type": "CONDITIONING", "link": null}, {"name": "latent_image", "type": "LATENT", "link": null}, {"name": "seed", "type": "INT", "link": null, "widget": {"name": "seed"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [16, 81], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [873538233761307, "fixed", 20, 8, "euler", "normal", 1]}, {"id": 92, "type": "SelfAttentionGuidance", "pos": [-7.052225788990594, 683.5517590383425], "size": [290, 80], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": null}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [124], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SelfAttentionGuidance"}, "widgets_values": [0.5, 2]}, {"id": 28, "type": "SVD_img2vid_Conditioning", "pos": [-968.6773160171516, 1871.8887333222312], "size": {"0": 220, "1": 220}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 27}, {"name": "init_image", "type": "IMAGE", "link": 33, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 29, "slot_index": 2}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [30], "shape": 3, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [31], "shape": 3, "slot_index": 1}, {"name": "latent", "type": "LATENT", "links": [32], "shape": 3, "slot_index": 2}], "properties": {"Node name for S&R": "SVD_img2vid_Conditioning"}, "widgets_values": [1024, 576, 14, 50, 15, 0]}, {"id": 38, "type": "AlphaChanelRemove", "pos": [-493.6637709418091, 2271.************], "size": {"0": 210, "1": 26}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 55}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [126], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "AlphaChanelRemove"}}, {"id": 49, "type": "AlphaChanelRemove", "pos": [854.1032656252662, 2271.0945216146683], "size": {"0": 210, "1": 26}, "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 57}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [127], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "AlphaChanelRemove"}}, {"id": 53, "type": "AlphaChanelRemove", "pos": [853.0857364050092, 2843.7697943281187], "size": {"0": 210, "1": 26}, "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 61}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [128], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "AlphaChanelRemove"}}, {"id": 50, "type": "ImageCompositeBy_Zho", "pos": [523.0857364050071, 2843.7697943281187], "size": {"0": 315, "1": 270}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "images_a", "type": "IMAGE", "link": 63}, {"name": "images_b", "type": "IMAGE", "link": 64}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [61], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageCompositeBy_Zho"}, "widgets_values": [1, 1, 0.98, 1, 0, 0, "images_b", "max", "matrix"]}, {"id": 96, "type": "SaveAnimatedWEBP", "pos": [857.4074467287679, 2345.8280560315607], "size": {"0": 440, "1": 400}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 127}], "properties": {"Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 12, true, 90, "default", null]}, {"id": 97, "type": "SaveAnimatedWEBP", "pos": [851.6458945582993, 2914.7769473672647], "size": {"0": 440, "1": 400}, "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 128}], "properties": {"Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 12, true, 90, "default", null]}, {"id": 94, "type": "SaveAnimatedWEBP", "pos": [458.0449174874797, 1769.0408031630598], "size": {"0": 440, "1": 400}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 125}], "properties": {"Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 12, true, 90, "default", null]}, {"id": 35, "type": "ImageCompositeBy_Zho", "pos": [-831.6637709418, 2272.************], "size": {"0": 315, "1": 270}, "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "images_a", "type": "IMAGE", "link": 48}, {"name": "images_b", "type": "IMAGE", "link": 47}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [55], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageCompositeBy_Zho"}, "widgets_values": [1, 1, 1, 0.05, 0, 0, "images_b", "max", "pair"]}, {"id": 45, "type": "ImageCompositeBy_Zho", "pos": [524.1032656252642, 2271.0945216146683], "size": {"0": 315, "1": 270}, "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "images_a", "type": "IMAGE", "link": 51}, {"name": "images_b", "type": "IMAGE", "link": 59}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [57, 64], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageCompositeBy_Zho"}, "widgets_values": [1, 1, 1, 0.05, 0, 0, "images_b", "max", "pair"]}, {"id": 36, "type": "Text_Image_Frame_Zho", "pos": [-1370.4561994402377, 2271.8328108783735], "size": {"0": 280, "1": 420}, "flags": {}, "order": 8, "mode": 0, "outputs": [{"name": "image", "type": "IMAGE", "links": [48, 49], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Text_Image_Frame_Zho"}, "widgets_values": ["1", 14, 1, "OverlockSC-Regular", "center", 10, 50, "#ffffff", 0, "blue", 0, 0, 60, 50, false]}, {"id": 95, "type": "SaveAnimatedWEBP", "pos": [-500.4561994402365, 2341.8328108783735], "size": {"0": 440, "1": 400}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 126}], "properties": {"Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 8, true, 90, "default", null]}, {"id": 33, "type": "GMFSS Fortuna VFI", "pos": [148.64182769746415, 1779.4893208533256], "size": {"0": 300, "1": 130}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "frames", "type": "IMAGE", "link": 38}, {"name": "optional_interpolation_states", "type": "INTERPOLATION_STATES", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [59, 125], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "GMFSS Fortuna VFI"}, "widgets_values": ["GMFSS_fortuna_union", 10, 2]}, {"id": 76, "type": "Fast Groups Bypasser (rgthree)", "pos": {"0": -1083.222412109375, "1": 1324.340576171875, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [250, 350], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "OPT_CONNECTION", "type": "*", "links": null}], "properties": {"matchColors": "", "matchTitle": "", "showNav": true, "sort": "position", "customSortAlphabet": ""}, "color": "#323", "bgcolor": "#535"}, {"id": 17, "type": "PortraitMaster_中文版", "pos": [-790, 680], "size": [540, 990], "flags": {}, "order": 10, "mode": 0, "outputs": [{"name": "positive", "type": "STRING", "links": [98], "shape": 3, "slot_index": 0}, {"name": "negative", "type": "STRING", "links": [84], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "PortraitMaster_中文版"}, "widgets_values": ["肩部以上肖像", 1.46, "女性", "中国人", "-", 0.5, "灰色", "开心", 1.42, "椭圆形", 0.78, 0.2, "法式波波头", "黑色", 0.67, 21, 0.53, 0.36, 0.44, 0, 0, 0, 1.16, 1.17, 1.2, 1.16, "摄影棚灯光", "从左侧", 1.17, "disable", "xxmixgirl, raw photo, (realistic:1.5)", "(white background:1.5)", "", ""]}, {"id": 93, "type": "SaveAnimatedWEBP", "pos": [-328.45212823055084, 1773.3789129997735], "size": [440, 400], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 123}], "properties": {"Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 8, true, 90, "default", null]}, {"id": 37, "type": "Text_Image_Zho", "pos": [-9.538601315504453, 2850.384447084436], "size": {"0": 290, "1": 370}, "flags": {}, "order": 11, "mode": 0, "outputs": [{"name": "image", "type": "IMAGE", "links": [62, 63], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Text_Image_Zho"}, "widgets_values": ["-Zho-", "Alkatra", "center", 100, 50, "#ffffff", 0, "blue", 0, 0, 100, 50, false]}, {"id": 51, "type": "PreviewImage", "pos": [290.4613986844954, 2850.384447084436], "size": {"0": 210, "1": 246}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 62}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 13, "type": "PreviewImage", "pos": [-7.052225788990594, 983.5517590383425], "size": [750, 690], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 18}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 20, "type": "Fast Groups Muter (rgthree)", "pos": {"0": -1363.22216796875, "1": 1324.340576171875, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [270, 350], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "OPT_CONNECTION", "type": "*", "links": null}], "properties": {"matchColors": "", "matchTitle": "", "showNav": true, "sort": "position", "customSortAlphabet": ""}, "color": "#232", "bgcolor": "#353"}], "links": [[16, 14, 0, 15, 0, "LATENT"], [18, 15, 0, 13, 0, "IMAGE"], [25, 26, 0, 25, 0, "MODEL"], [26, 25, 0, 27, 0, "MODEL"], [27, 26, 1, 28, 0, "CLIP_VISION"], [29, 26, 2, 28, 2, "VAE"], [30, 28, 0, 27, 1, "CONDITIONING"], [31, 28, 1, 27, 2, "CONDITIONING"], [32, 28, 2, 27, 3, "LATENT"], [33, 29, 0, 28, 1, "IMAGE"], [35, 27, 0, 31, 0, "LATENT"], [36, 26, 2, 31, 1, "VAE"], [38, 31, 0, 33, 0, "IMAGE"], [40, 15, 0, 39, 0, "IMAGE"], [41, 15, 0, 40, 0, "IMAGE"], [42, 39, 0, 41, 0, "*"], [43, 40, 0, 41, 1, "IMAGE"], [47, 31, 0, 35, 1, "IMAGE"], [48, 36, 0, 35, 0, "IMAGE"], [49, 36, 0, 43, 0, "IMAGE"], [51, 46, 0, 45, 0, "IMAGE"], [53, 46, 0, 47, 0, "IMAGE"], [55, 35, 0, 38, 0, "IMAGE"], [57, 45, 0, 49, 0, "IMAGE"], [59, 33, 0, 45, 1, "IMAGE"], [61, 50, 0, 53, 0, "IMAGE"], [62, 37, 0, 51, 0, "IMAGE"], [63, 37, 0, 50, 0, "IMAGE"], [64, 45, 0, 50, 1, "IMAGE"], [65, 4, 0, 55, 0, "*"], [66, 4, 1, 55, 1, "*"], [67, 4, 2, 55, 2, "*"], [68, 5, 0, 54, 0, "*"], [69, 16, 0, 56, 0, "*"], [70, 7, 0, 56, 1, "*"], [81, 14, 0, 39, 1, "LATENT"], [84, 17, 1, 65, 0, "*"], [98, 17, 0, 24, 0, "STRING"], [99, 24, 0, 64, 0, "*"], [123, 31, 0, 93, 0, "IMAGE"], [124, 92, 0, 14, 0, "MODEL"], [125, 33, 0, 94, 0, "IMAGE"], [126, 38, 0, 95, 0, "IMAGE"], [127, 49, 0, 96, 0, "IMAGE"], [128, 53, 0, 97, 0, "IMAGE"], [131, 41, 0, 29, 0, "IMAGE"]], "groups": [{"title": "<PERSON><PERSON><PERSON>", "bounding": [761, 607, 430, 594], "color": "#8AA", "font_size": 24}, {"title": "1st Batchget", "bounding": [760, 1209, 230, 145], "color": "#8AA", "font_size": 24}, {"title": "WaterMark", "bounding": [-27, 2769, 1336, 562], "color": "#88A", "font_size": 24}, {"title": "ITP Frame <PERSON>um", "bounding": [-25, 2197, 1333, 559], "color": "#b06634", "font_size": 24}, {"title": "<PERSON><PERSON>", "bounding": [-1383, 2197, 1342, 560], "color": "#A88", "font_size": 24}, {"title": "ITP", "bounding": [138, 1696, 770, 484], "color": "#b06634", "font_size": 24}, {"title": "SVD output", "bounding": [-339, 1699, 460, 484], "color": "#A88", "font_size": 24}, {"title": "SVD", "bounding": [-1379, 1698, 1027, 486], "color": "#A88", "font_size": 24}, {"title": "1st choose", "bounding": [760, 1365, 230, 190], "color": "#8AA", "font_size": 24}, {"title": "Basic", "bounding": [-1374, 606, 552, 634], "color": "#444", "font_size": 24}, {"title": "Prompt", "bounding": [-800, 606, 774, 1075], "color": "#8AA", "font_size": 24}, {"title": "1st", "bounding": [-15, 609, 765, 1075], "color": "#8AA", "font_size": 24}, {"title": "Control", "bounding": [-1373, 1250, 550, 434], "color": "#a1309b", "font_size": 24}], "config": {}, "extra": {}, "version": 0.4}