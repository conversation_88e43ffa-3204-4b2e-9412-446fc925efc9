{"id": "3d0bda3a-fa27-456f-8179-6b7225b28ca7", "revision": 0, "last_node_id": 25, "last_link_id": 46, "nodes": [{"id": 10, "type": "PreviewImage", "pos": [1836, 1408], "size": [210, 250], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 20}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 22, "type": "MaskPreview+", "pos": [792, 1270], "size": [210, 246], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 45}], "outputs": [], "properties": {"cnr_id": "comfyui_essentials", "ver": "9d9f4bedfc9f0321c19faf71855e228c93bd0dc9", "Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 4, "type": "PreviewImage", "pos": [1990, 170], "size": [390, 520], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 4}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 8, "type": "GetImageSize+", "pos": [1494.6317138671875, 1087], "size": [210, 66], "flags": {}, "order": 2, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 37}], "outputs": [{"label": "width", "localized_name": "width", "name": "width", "type": "INT", "slot_index": 0, "links": [8]}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "slot_index": 1, "links": [9]}, {"label": "count", "localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "9d9f4bedfc9f0321c19faf71855e228c93bd0dc9", "Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 11, "type": "LayerUtility: <PERSON><PERSON><PERSON>", "pos": [1395, 1254], "size": [315, 174], "flags": {}, "order": 3, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 35}, {"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 36}, {"localized_name": "lama_model", "name": "lama_model", "type": "COMBO", "widget": {"name": "lama_model"}, "link": null}, {"localized_name": "device", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "invert_mask", "name": "invert_mask", "type": "BOOLEAN", "widget": {"name": "invert_mask"}, "link": null}, {"localized_name": "mask_grow", "name": "mask_grow", "type": "INT", "widget": {"name": "mask_grow"}, "link": null}, {"localized_name": "mask_blur", "name": "mask_blur", "type": "INT", "widget": {"name": "mask_blur"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [20, 21]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerUtility: <PERSON><PERSON><PERSON>"}, "widgets_values": ["lama", "cuda", false, 30, 25], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 18, "type": "InvertMask", "pos": [713.8318481445312, 901], "size": [210, 26], "flags": {}, "order": 4, "mode": 0, "inputs": [{"label": "mask", "localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 42}], "outputs": [{"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 0, "links": [41]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "InvertMask"}, "widgets_values": []}, {"id": 17, "type": "MaskToImage", "pos": [1108.5743408203125, 905.9638671875], "size": [264.5999755859375, 26], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "mask", "localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 41}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [40]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 7, "type": "ImageScale", "pos": [1910.4888916015625, 1058.9779052734375], "size": [320, 130], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 11}, {"localized_name": "缩放算法", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"label": "width", "localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 8}, {"label": "height", "localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 9}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [12]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 512, 512, "disabled"]}, {"id": 23, "type": "SaveImage", "pos": [2663.082275390625, 1042.9112548828125], "size": [370, 520], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "images", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 46}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29"}, "widgets_values": ["ComfyUI"]}, {"id": 5, "type": "Image Blending Mode", "pos": [2269, 1188], "size": [315, 102], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "image_a", "localized_name": "image_a", "name": "image_a", "type": "IMAGE", "link": 12}, {"label": "image_b", "localized_name": "image_b", "name": "image_b", "type": "IMAGE", "link": 21}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "blend_percentage", "name": "blend_percentage", "type": "FLOAT", "widget": {"name": "blend_percentage"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [46]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Image Blending Mode"}, "widgets_values": ["overlay", 0.5]}, {"id": 1, "type": "LoadImage", "pos": [-200, 805], "size": [320, 315], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [1, 38]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadImage"}, "widgets_values": ["R-C.jpg", "image"]}, {"id": 2, "type": "ComfyWordCloud", "pos": [1445.440185546875, 361.2037353515625], "size": [400, 612], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "color_ref_image", "localized_name": "color_ref_image", "name": "color_ref_image", "shape": 7, "type": "IMAGE", "link": 1}, {"label": "mask_image", "localized_name": "mask_image", "name": "mask_image", "shape": 7, "type": "IMAGE", "link": 40}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "scale", "name": "scale", "type": "FLOAT", "widget": {"name": "scale"}, "link": null}, {"localized_name": "margin", "name": "margin", "type": "INT", "widget": {"name": "margin"}, "link": null}, {"localized_name": "font_path", "name": "font_path", "type": "COMBO", "widget": {"name": "font_path"}, "link": null}, {"localized_name": "min_font_size", "name": "min_font_size", "type": "INT", "widget": {"name": "min_font_size"}, "link": null}, {"localized_name": "max_font_size", "name": "max_font_size", "type": "INT", "widget": {"name": "max_font_size"}, "link": null}, {"localized_name": "relative_scaling", "name": "relative_scaling", "type": "FLOAT", "widget": {"name": "relative_scaling"}, "link": null}, {"localized_name": "colormap", "name": "colormap", "type": "COMBO", "widget": {"name": "colormap"}, "link": null}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}, {"localized_name": "transparent_background", "name": "transparent_background", "type": "BOOLEAN", "widget": {"name": "transparent_background"}, "link": null}, {"localized_name": "prefer_horizontal", "name": "prefer_horizontal", "type": "FLOAT", "widget": {"name": "prefer_horizontal"}, "link": null}, {"localized_name": "max_words", "name": "max_words", "type": "INT", "widget": {"name": "max_words"}, "link": null}, {"localized_name": "repeat", "name": "repeat", "type": "BOOLEAN", "widget": {"name": "repeat"}, "link": null}, {"localized_name": "include_numbers", "name": "include_numbers", "type": "BOOLEAN", "widget": {"name": "include_numbers"}, "link": null}, {"localized_name": "random_state", "name": "random_state", "type": "INT", "widget": {"name": "random_state"}, "link": null}, {"localized_name": "stopwords", "name": "stopwords", "type": "STRING", "widget": {"name": "stopwords"}, "link": null}, {"localized_name": "contour_width", "name": "contour_width", "shape": 7, "type": "FLOAT", "widget": {"name": "contour_width"}, "link": null}, {"localized_name": "contour_color", "name": "contour_color", "shape": 7, "type": "STRING", "widget": {"name": "contour_color"}, "link": null}, {"localized_name": "keynote_words", "name": "keynote_words", "shape": 7, "type": "STRING", "widget": {"name": "keynote_words"}, "link": null}, {"localized_name": "keynote_weight", "name": "keynote_weight", "shape": 7, "type": "INT", "widget": {"name": "keynote_weight"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [4, 11]}, {"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfyui_wordcloud", "ver": "9a20688fae496858f6c0e3c4bf591a3fa5198c9f", "Node name for S&R": "ComfyWordCloud"}, "widgets_values": ["Interstellar, Black-hole, Hawking, LOVE\n", 512, 512, 4, 0, "arial.ttf", 4, 128, 0.5, "viridis", "#FFFFFF", true, 0.9, 200, true, false, -1, "", 0, "#000000", "", 60]}, {"id": 16, "type": "LayerMask: SegmentAnythingUltra V2", "pos": [186, 984], "size": [428.4000244140625, 366], "flags": {}, "order": 1, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 38}, {"localized_name": "sam_model", "name": "sam_model", "type": "COMBO", "widget": {"name": "sam_model"}, "link": null}, {"localized_name": "grounding_dino_model", "name": "grounding_dino_model", "type": "COMBO", "widget": {"name": "grounding_dino_model"}, "link": null}, {"localized_name": "threshold", "name": "threshold", "type": "FLOAT", "widget": {"name": "threshold"}, "link": null}, {"localized_name": "detail_method", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "detail_erode", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "detail_dilate", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "black_point", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "white_point", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "process_detail", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "device", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "max_megapixels", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}, {"localized_name": "cache_model", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [35, 37]}, {"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 1, "links": [36, 42, 45]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerMask: SegmentAnythingUltra V2"}, "widgets_values": ["sam_vit_h (2.56GB)", "GroundingDINO_SwinT_OGC (694MB)", 0.2, "VITMatte", 6, 6, 0.15, 0.99, true, "blackhole", "cuda", 2, false], "color": "rgba(27, 80, 119, 0.7)"}], "links": [[1, 1, 0, 2, 0, "IMAGE"], [4, 2, 0, 4, 0, "IMAGE"], [8, 8, 0, 7, 2, "INT"], [9, 8, 1, 7, 3, "INT"], [11, 2, 0, 7, 0, "IMAGE"], [12, 7, 0, 5, 0, "IMAGE"], [20, 11, 0, 10, 0, "IMAGE"], [21, 11, 0, 5, 1, "IMAGE"], [35, 16, 0, 11, 0, "IMAGE"], [36, 16, 1, 11, 1, "MASK"], [37, 16, 0, 8, 0, "IMAGE"], [38, 1, 0, 16, 0, "IMAGE"], [40, 17, 0, 2, 1, "IMAGE"], [41, 18, 0, 17, 0, "MASK"], [42, 16, 1, 18, 0, "MASK"], [45, 16, 1, 22, 0, "MASK"], [46, 5, 0, 23, 0, "IMAGE"]], "groups": [{"id": 1, "title": "  黎黎原上咩（SweetValberry）", "bounding": [355.380126953125, 461.1520080566406, 824, 80], "color": "#ffffff", "font_size": 60, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.4420993610650663, "offset": [-49.42777395149597, -1071.2531976028656]}, "workspace_info": {"id": "ON2rCnr1pE8Q_QVcFJ7tW"}, "frontendVersion": "1.17.11", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}