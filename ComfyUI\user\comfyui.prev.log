## ComfyUI-Manager: installing dependencies done.
[2025-08-31 20:42:52.745] ** ComfyUI startup time: 2025-08-31 20:42:52.745
[2025-08-31 20:42:52.745] ** Platform: Windows
[2025-08-31 20:42:52.746] ** Python version: 3.13.6 (tags/v3.13.6:4e66535, Aug  6 2025, 14:36:00) [MSC v.1944 64 bit (AMD64)]
[2025-08-31 20:42:52.746] ** Python executable: C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\python.exe
[2025-08-31 20:42:52.747] ** ComfyUI Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 20:42:52.747] ** ComfyUI Base Folder Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 20:42:52.747] ** User directory: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user
[2025-08-31 20:42:52.756] ** ComfyUI-Manager config path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-08-31 20:42:52.757] ** Log path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-08-31 20:42:53.622]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\rgthree-comfy
[2025-08-31 20:42:53.622]    1.9 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Manager-main
[2025-08-31 20:42:53.622] 
[2025-08-31 20:42:55.474] Checkpoint files will always be loaded safely.
[2025-08-31 20:42:58.023] Total VRAM 8151 MB, total RAM 32472 MB
[2025-08-31 20:42:58.023] pytorch version: 2.8.0+cu129
[2025-08-31 20:42:58.023] Set vram state to: NORMAL_VRAM
[2025-08-31 20:42:58.024] Device: cuda:0 NVIDIA GeForce RTX 5060 Laptop GPU : cudaMallocAsync
[2025-08-31 20:43:01.744] Using pytorch attention
[2025-08-31 20:43:10.468] Python version: 3.13.6 (tags/v3.13.6:4e66535, Aug  6 2025, 14:36:00) [MSC v.1944 64 bit (AMD64)]
[2025-08-31 20:43:10.469] ComfyUI version: 0.3.56
[2025-08-31 20:43:10.638] ComfyUI frontend version: 1.25.11
[2025-08-31 20:43:10.648] [Prompt Server] web root: C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-08-31 20:43:16.350] [Crystools [0;32mINFO[0m] Crystools version: 1.27.3
[2025-08-31 20:43:16.483] [Crystools [0;32mINFO[0m] Platform release: 11
[2025-08-31 20:43:16.484] [Crystools [0;32mINFO[0m] JETSON: Not detected.
[2025-08-31 20:43:16.485] [Crystools [0;32mINFO[0m] CPU: Intel(R) Core(TM) i7-14650HX - Arch: AMD64 - OS: Windows 11
[2025-08-31 20:43:16.494] [Crystools [0;32mINFO[0m] pynvml (NVIDIA) initialized.
[2025-08-31 20:43:16.495] [Crystools [0;32mINFO[0m] GPU/s:
[2025-08-31 20:43:16.508] [Crystools [0;32mINFO[0m] 0) NVIDIA GeForce RTX 5060 Laptop GPU
[2025-08-31 20:43:16.508] [Crystools [0;32mINFO[0m] NVIDIA Driver: 573.01
[2025-08-31 20:43:16.519] Total VRAM 8151 MB, total RAM 32472 MB
[2025-08-31 20:43:16.520] pytorch version: 2.8.0+cu129
[2025-08-31 20:43:16.520] Set vram state to: NORMAL_VRAM
[2025-08-31 20:43:16.520] Device: cuda:0 NVIDIA GeForce RTX 5060 Laptop GPU : cudaMallocAsync
[2025-08-31 20:43:16.609] ### Loading: ComfyUI-Manager (V3.36)
[2025-08-31 20:43:16.610] [ComfyUI-Manager] network_mode: public
[2025-08-31 20:43:16.690] ### ComfyUI Revision: 150 [4449e147] *DETACHED | Released on '2025-08-30'
[2025-08-31 20:43:17.693] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-08-31 20:43:17.748] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-08-31 20:43:18.139] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-08-31 20:43:18.209] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-08-31 20:43:18.578] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-08-31 20:43:28.123] FETCH ComfyRegistry Data: 5/96
[2025-08-31 20:43:28.678] ------------------------------------------
[2025-08-31 20:43:28.679] Comfyroll Studio v1.76 :  175 Nodes Loaded
[2025-08-31 20:43:28.680] ------------------------------------------
[2025-08-31 20:43:28.681] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-08-31 20:43:28.681] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-08-31 20:43:28.681] ------------------------------------------
[2025-08-31 20:43:28.698] [C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts
[2025-08-31 20:43:28.703] [C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False
[2025-08-31 20:43:28.706] [C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider']
[2025-08-31 20:43:28.873] C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-08-31 20:43:29.132] # 😺dzNodes: LayerStyle -> Cannot import name 'guidedFilter' from 'cv2.ximgproc'
A few nodes cannot works properly, while most nodes are not affected. Please REINSTALL package 'opencv-contrib-python'.
For detail refer to https://github.com/chflame163/ComfyUI_LayerStyle/issues/5
[2025-08-31 20:43:34.553] C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\timm\models\layers\__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
  warnings.warn(f"Importing from {__name__} is deprecated, please import via timm.layers", FutureWarning)
[2025-08-31 20:43:35.273] C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-08-31 20:43:35.875] FETCH ComfyRegistry Data: 10/96
[2025-08-31 20:43:35.887] # 😺dzNodes: WordCloud ->  find 345 fonts in C:\Windows\fonts
[2025-08-31 20:43:37.540] 
[2025-08-31 20:43:37.540] [rgthree-comfy] Loaded 48 epic nodes. 🎉
[2025-08-31 20:43:37.540] 
[2025-08-31 20:43:38.569] WAS Node Suite: OpenCV Python FFMPEG support is enabled
[2025-08-31 20:43:38.570] WAS Node Suite Warning: `ffmpeg_bin_path` is not set in `C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-node-suite-comfyui\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.
[2025-08-31 20:43:38.948] WAS Node Suite: Finished. Loaded 220 nodes successfully.
[2025-08-31 20:43:38.949] 
	"The future depends on what you do today." - Mahatma Gandhi
[2025-08-31 20:43:38.949] 
[2025-08-31 20:43:38.953] 
Import times for custom nodes:
[2025-08-31 20:43:38.953]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-08-31 20:43:38.954]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_AdvancedRefluxControl
[2025-08-31 20:43:38.954]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_essentials
[2025-08-31 20:43:38.955]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-08-31 20:43:38.955]    0.1 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-ic-light
[2025-08-31 20:43:38.955]    0.1 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Manager-main
[2025-08-31 20:43:38.955]    0.4 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-08-31 20:43:38.955]    0.7 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_wordcloud
[2025-08-31 20:43:38.955]    0.8 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Crystools
[2025-08-31 20:43:38.955]    1.4 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-node-suite-comfyui
[2025-08-31 20:43:38.955]    1.5 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-08-31 20:43:38.955]    1.6 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\rgthree-comfy
[2025-08-31 20:43:38.955]    6.1 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle
[2025-08-31 20:43:38.955]   10.5 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_CatVTON_Wrapper
[2025-08-31 20:43:38.955] 
[2025-08-31 20:43:40.072] Context impl SQLiteImpl.
[2025-08-31 20:43:40.072] Will assume non-transactional DDL.
[2025-08-31 20:43:40.074] No target revision found.
[2025-08-31 20:43:40.158] Starting server

[2025-08-31 20:43:40.160] To see the GUI go to: http://127.0.0.1:8188
[2025-08-31 20:43:42.334] FETCH ComfyRegistry Data: 15/96
[2025-08-31 20:43:50.127] FETCH ComfyRegistry Data: 20/96
[2025-08-31 20:43:57.075] FETCH ComfyRegistry Data: 25/96
[2025-08-31 20:44:03.973] FETCH ComfyRegistry Data: 30/96
[2025-08-31 20:44:10.821] FETCH ComfyRegistry Data: 35/96
[2025-08-31 20:44:18.881] FETCH ComfyRegistry Data: 40/96
[2025-08-31 20:44:24.668] FETCH ComfyRegistry Data: 45/96
[2025-08-31 20:44:30.473] FETCH ComfyRegistry Data: 50/96
[2025-08-31 20:44:36.739] FETCH ComfyRegistry Data: 55/96
[2025-08-31 20:44:43.744] FETCH ComfyRegistry Data: 60/96
[2025-08-31 20:44:51.216] got prompt
[2025-08-31 20:44:51.779] FETCH ComfyRegistry Data: 65/96
[2025-08-31 20:44:59.292] FETCH ComfyRegistry Data: 70/96
[2025-08-31 20:45:05.384] FETCH ComfyRegistry Data: 75/96
[2025-08-31 20:45:14.988] final text_encoder_type: bert-base-uncased
[2025-08-31 20:45:16.024] FETCH ComfyRegistry Data: 80/96
[2025-08-31 20:45:21.918] FETCH ComfyRegistry Data: 85/96
[2025-08-31 20:45:23.999] C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\transformers\modeling_utils.py:1773: FutureWarning: The `device` argument is deprecated and will be removed in v5 of Transformers.
  warnings.warn(
[2025-08-31 20:45:27.669] C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\local_groundingdino\models\GroundingDINO\transformer.py:862: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with torch.cuda.amp.autocast(enabled=False):
[2025-08-31 20:45:29.162] FETCH ComfyRegistry Data: 90/96
[2025-08-31 20:45:37.618] FETCH ComfyRegistry Data: 95/96
[2025-08-31 20:45:39.408] FETCH ComfyRegistry Data [DONE]
[2025-08-31 20:45:41.024] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-08-31 20:45:41.149] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-08-31 20:45:43.069] [ComfyUI-Manager] All startup tasks have been completed.
[2025-08-31 20:45:44.234] # 😺dzNodes: LayerStyle -> SegmentAnythingUltra V2 Processed 1 image(s).
[2025-08-31 20:45:44.376] # 😺dzNodes: LayerStyle -> lama copy
[2025-08-31 20:45:44.424] # 😺dzNodes: LayerStyle -> config file written: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\temp\_lama_xijye74bjkl8lxqf_temp\config.json
[2025-08-31 20:45:48.842] C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\ldm.py:279: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  @torch.cuda.amp.autocast()
[2025-08-31 20:45:49.100] C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\ldm.py:279: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  @torch.cuda.amp.autocast()
[2025-08-31 20:45:49.496] 2025-08-31 20:45:49.495 | INFO     | C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle.py.iopaint.model_manager:init_model:38 - Loading model: lama
[2025-08-31 20:45:49.499] 2025-08-31 20:45:49.498 | INFO     | C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle.py.iopaint.helper:load_jit_model:109 - Loading model from: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\models\lama\big-lama.pt
[2025-08-31 20:46:07.092]   Batch processing... ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100% 1/1 0:00:10
[2025-08-31 20:46:07.420] # 😺dzNodes: LayerStyle -> LaMa Processed 1 image(s).
[2025-08-31 20:46:07.552] Building prefix dict from the default dictionary ...
[2025-08-31 20:46:07.553] Building prefix dict from the default dictionary ...
[2025-08-31 20:46:07.554] Loading model from cache C:\Users\<USER>\AppData\Local\Temp\jieba.cache
[2025-08-31 20:46:07.556] Loading model from cache C:\Users\<USER>\AppData\Local\Temp\jieba.cache
[2025-08-31 20:46:10.210] Loading model cost 2.656 seconds.
[2025-08-31 20:46:10.222] Loading model cost 2.656 seconds.
[2025-08-31 20:46:10.222] Prefix dict has been built successfully.
[2025-08-31 20:46:10.225] Prefix dict has been built successfully.
[2025-08-31 20:46:10.228] # 😺dzNodes: WordCloud ->  word frequencies dict generated, include 1 words.
[2025-08-31 20:46:10.234] # 😺dzNodes: WordCloud ->  font_path = C:\Windows\fonts\arial.ttf
[2025-08-31 20:46:29.997] Prompt executed in 98.77 seconds
[2025-08-31 20:47:00.585] got prompt
[2025-08-31 20:47:00.687] # 😺dzNodes: WordCloud ->  word frequencies dict generated, include 1 words.
[2025-08-31 20:47:00.691] # 😺dzNodes: WordCloud ->  font_path = C:\Windows\fonts\arial.ttf
[2025-08-31 20:47:19.514] Prompt executed in 18.92 seconds
[2025-08-31 20:47:58.313] got prompt
[2025-08-31 20:47:58.380] # 😺dzNodes: WordCloud ->  word frequencies dict generated, include 3 words.
[2025-08-31 20:47:58.382] # 😺dzNodes: WordCloud ->  font_path = C:\Windows\fonts\arial.ttf
[2025-08-31 20:48:17.802] Prompt executed in 19.49 seconds
[2025-08-31 21:11:39.492] [Crystools [0;31mERROR[0m] Could not get GPU utilization. Unknown Error
[2025-08-31 21:11:39.494] [Crystools [0;31mERROR[0m] Monitor of GPU is turning off.
[2025-08-31 21:25:51.657] got prompt
[2025-08-31 21:25:51.727] # 😺dzNodes: WordCloud ->  word frequencies dict generated, include 4 words.
[2025-08-31 21:25:51.729] # 😺dzNodes: WordCloud ->  font_path = C:\Windows\fonts\arial.ttf
[2025-08-31 21:26:20.921] Prompt executed in 29.26 seconds
[2025-08-31 21:28:13.570] got prompt
[2025-08-31 21:28:14.311] Prompt executed in 0.74 seconds
[2025-08-31 21:28:19.944] got prompt
[2025-08-31 21:28:20.714] Prompt executed in 0.77 seconds
[2025-08-31 21:28:31.303] got prompt
[2025-08-31 21:28:32.018] Prompt executed in 0.71 seconds
[2025-08-31 21:28:38.151] got prompt
[2025-08-31 21:28:38.937] Prompt executed in 0.78 seconds
[2025-08-31 21:29:24.320] 
Stopped server
