{"models": [{"base": "FLUX.1", "description": "(FLUX.1 Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "filename": "taef1_decoder.pth", "name": "TAEF1 Decoder", "reference": "https://github.com/madebyollin/taesd", "save_path": "vae_approx", "size": "4.71MB", "type": "TAESD", "url": "https://github.com/madebyollin/taesd/raw/main/taef1_decoder.pth"}, {"base": "FLUX.1", "description": "(FLUX.1 Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "filename": "taef1_encoder.pth", "name": "TAEF1 Encoder", "reference": "https://github.com/madebyollin/taesd", "save_path": "vae_approx", "size": "4.71MB", "type": "TAESD", "url": "https://github.com/madebyollin/taesd/raw/main/taef1_encoder.pth"}, {"base": "SD3", "description": "(SD3 Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "filename": "taesd3_decoder.pth", "name": "TAESD3 Decoder", "reference": "https://github.com/madebyollin/taesd", "save_path": "vae_approx", "size": "4.94MB", "type": "TAESD", "url": "https://github.com/madebyollin/taesd/raw/main/taesd3_decoder.pth"}, {"base": "SD3", "description": "(SD3 Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "filename": "taesd3_encoder.pth", "name": "TAESD3 Encoder", "reference": "https://github.com/madebyollin/taesd", "save_path": "vae_approx", "size": "4.94MB", "type": "TAESD", "url": "https://github.com/madebyollin/taesd/raw/main/taesd3_encoder.pth"}, {"base": "SDXL", "description": "(SDXL Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "filename": "taesdxl_decoder.pth", "name": "TAESDXL Decoder", "reference": "https://github.com/madebyollin/taesd", "save_path": "vae_approx", "size": "4.91MB", "type": "TAESD", "url": "https://github.com/madebyollin/taesd/raw/main/taesdxl_decoder.pth"}, {"base": "SDXL", "description": "(SDXL Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "filename": "taesdxl_encoder.pth", "name": "TAESDXL Encoder", "reference": "https://github.com/madebyollin/taesd", "save_path": "vae_approx", "size": "4.91MB", "type": "TAESD", "url": "https://github.com/madebyollin/taesd/raw/main/taesdxl_encoder.pth"}, {"base": "SD1.x", "description": "To view the preview in high quality while running samples in ComfyUI, you will need this model.", "filename": "taesd_decoder.pth", "name": "TAESD Decoder", "reference": "https://github.com/madebyollin/taesd", "save_path": "vae_approx", "size": "4.91MB", "type": "TAESD", "url": "https://github.com/madebyollin/taesd/raw/main/taesd_decoder.pth"}, {"base": "SD1.x", "description": "To view the preview in high quality while running samples in ComfyUI, you will need this model.", "filename": "taesd_encoder.pth", "name": "TAESD Encoder", "reference": "https://github.com/madebyollin/taesd", "save_path": "vae_approx", "size": "4.91MB", "type": "TAESD", "url": "https://github.com/madebyollin/taesd/raw/main/taesd_encoder.pth"}, {"base": "upscale", "description": "RealESRGAN x2 upscaler model", "filename": "RealESRGAN_x2.pth", "name": "RealESRGAN x2", "reference": "https://huggingface.co/ai-forever/Real-ESRGAN", "save_path": "default", "size": "67.1MB", "type": "upscale", "url": "https://huggingface.co/ai-forever/Real-ESRGAN/resolve/main/RealESRGAN_x2.pth"}, {"base": "upscale", "description": "RealESRGAN x4 upscaler model", "filename": "RealESRGAN_x4.pth", "name": "RealESRGAN x4", "reference": "https://huggingface.co/ai-forever/Real-ESRGAN", "save_path": "default", "size": "67.0MB", "type": "upscale", "url": "https://huggingface.co/ai-forever/Real-ESRGAN/resolve/main/RealESRGAN_x4.pth"}, {"base": "upscale", "description": "ESRGAN x4 upscaler model", "filename": "ESRGAN_4x.pth", "name": "ESRGAN x4", "reference": "https://huggingface.co/Afizi/ESRGAN_4x.pth", "save_path": "default", "size": "66.9MB", "type": "upscale", "url": "https://huggingface.co/Afizi/ESRGAN_4x.pth/resolve/main/ESRGAN_4x.pth"}, {"base": "upscale", "description": "4x_foolhardy_Remacri upscaler model", "filename": "4x_foolhardy_Remacri.pth", "name": "4x_foolhardy_<PERSON><PERSON><PERSON><PERSON>", "reference": "https://huggingface.co/FacehugmanIII/4x_foolhardy_Re<PERSON><PERSON>ri", "save_path": "default", "size": "67.0MB", "type": "upscale", "url": "https://huggingface.co/FacehugmanIII/4x_foolhardy_Remacri/resolve/main/4x_foolhardy_Remacri.pth"}, {"base": "upscale", "description": "4x-AnimeSharp upscaler model", "filename": "4x-AnimeSharp.pth", "name": "4x-AnimeSharp", "reference": "https://huggingface.co/Kim2091/AnimeSharp/", "save_path": "default", "size": "67.0MB", "type": "upscale", "url": "https://huggingface.co/Kim2091/AnimeSharp/resolve/main/4x-AnimeSharp.pth"}, {"base": "upscale", "description": "4x-UltraSharp upscaler model", "filename": "4x-UltraSharp.pth", "name": "4x-UltraSharp", "reference": "https://huggingface.co/Kim2091/UltraSharp/", "save_path": "default", "size": "67.0MB", "type": "upscale", "url": "https://huggingface.co/Kim2091/UltraSharp/resolve/main/4x-UltraSharp.pth"}, {"base": "upscale", "description": "4x_NMKD-Siax_200k upscaler model", "filename": "4x_NMKD-Siax_200k.pth", "name": "4x_NMKD-Siax_200k", "reference": "https://huggingface.co/gemasai/4x_NMKD-Siax_200k", "save_path": "default", "size": "67.0MB", "type": "upscale", "url": "https://huggingface.co/gemasai/4x_NMKD-Siax_200k/resolve/main/4x_NMKD-Siax_200k.pth"}, {"base": "upscale", "description": "8x_NMKD-Superscale_150000_G upscaler model", "filename": "8x_NMKD-Superscale_150000_G.pth", "name": "8x_NMKD-Superscale_150000_G", "reference": "https://huggingface.co/uwg/upscaler", "save_path": "default", "size": "67.1MB", "type": "upscale", "url": "https://huggingface.co/uwg/upscaler/resolve/main/ESRGAN/8x_NMKD-Superscale_150000_G.pth"}, {"base": "upscale", "description": "8x_NMKD-Faces_160000_G upscaler model", "filename": "8x_NMKD-Faces_160000_G.pth", "name": "8x_NMKD-Faces_160000_G", "reference": "https://huggingface.co/gemasai/8x_NMKD-Faces_160000_G/tree/main", "save_path": "default", "size": "67.2MB", "type": "upscale", "url": "https://huggingface.co/gemasai/8x_NMKD-Faces_160000_G/resolve/main/8x_NMKD-Faces_160000_G.pth"}, {"base": "upscale", "description": "LDSR upscale model. Through the [a/ComfyUI-Flowty-LDSR](https://github.com/flowtyone/ComfyUI-Flowty-LDSR) extension, the upscale model can be utilized.", "filename": "last.ckpt", "name": "LDSR(Latent Diffusion Super Resolution)", "reference": "https://github.com/CompVis/latent-diffusion", "save_path": "upscale_models/ldsr", "size": "2.04GB", "type": "upscale", "url": "https://heibox.uni-heidelberg.de/f/578df07c8fc04ffbadf3/?dl=1"}, {"base": "upscale", "description": "This upscaling model is a latent text-guided diffusion model and should be used with SD_4XUpscale_Conditioning and KSampler.", "filename": "x4-upscaler-ema.safetensors", "name": "stabilityai/stable-diffusion-x4-upscaler", "reference": "https://huggingface.co/stabilityai/stable-diffusion-x4-upscaler", "save_path": "checkpoints/upscale", "size": "3.53GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-diffusion-x4-upscaler/resolve/main/x4-upscaler-ema.safetensors"}, {"base": "deepbump", "description": "Checkpoint of the deepbump model to generate height and normal maps textures from an image (requires comfy_mtb)", "filename": "deepbump256.onnx", "name": "Deepbump", "reference": "https://github.com/HugoTini/DeepBump", "save_path": "deepbump", "size": "26.7MB", "type": "deepbump", "url": "https://github.com/HugoTini/DeepBump/raw/master/deepbump256.onnx"}, {"base": "face_restore", "description": "Face restoration", "filename": "GFPGANv1.3.pth", "name": "GFPGAN 1.3", "reference": "https://github.com/TencentARC/GFPGAN", "save_path": "face_restore", "size": "348.6MB", "type": "face_restore", "url": "https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.3.pth"}, {"base": "face_restore", "description": "Face restoration", "filename": "GFPGANv1.4.pth", "name": "GFPGAN 1.4", "reference": "https://github.com/TencentARC/GFPGAN", "save_path": "face_restore", "size": "348.6MB", "type": "face_restore", "url": "https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.4.pth"}, {"base": "face_restore", "description": "Face restoration", "filename": "RestoreFormer.pth", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reference": "https://github.com/TencentARC/GFPGAN", "save_path": "face_restore", "size": "290.8MB", "type": "face_restore", "url": "https://github.com/TencentARC/GFPGAN/releases/download/v1.3.4/RestoreFormer.pth"}, {"base": "SVD", "description": "Stable Video Diffusion (SVD) Image-to-Video is a diffusion model that takes in a still image as a conditioning frame, and generates a video from it.\nNOTE: 14 frames @ 576x1024", "filename": "svd.safetensors", "name": "Stable Video Diffusion Image-to-Video", "reference": "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid", "save_path": "checkpoints/SVD", "size": "9.56GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid/resolve/main/svd.safetensors"}, {"base": "zero123", "description": "Stable Zero123 is a model for view-conditioned image generation based on [a/Zero123](https://github.com/cvlab-columbia/zero123).", "filename": "stable_zero123.ckpt", "name": "stabilityai/Stable Zero123", "reference": "https://huggingface.co/stabilityai/stable-zero123", "save_path": "checkpoints/zero123", "size": "8.58GB", "type": "zero123", "url": "https://huggingface.co/stabilityai/stable-zero123/resolve/main/stable_zero123.ckpt"}, {"base": "SVD", "description": "Stable Video Diffusion (SVD) Image-to-Video is a diffusion model that takes in a still image as a conditioning frame, and generates a video from it.\nNOTE: 25 frames @ 576x1024 ", "filename": "svd_xt.safetensors", "name": "Stable Video Diffusion Image-to-Video (XT)", "reference": "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid-xt", "save_path": "checkpoints/SVD", "size": "9.56GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid-xt/resolve/main/svd_xt.safetensors"}, {"base": "SD1.5", "description": "If you use this embedding with negatives, you can solve the issue of damaging your hands.", "filename": "negative_hand-neg.pt", "name": "negative_hand Negative Embedding", "reference": "https://civitai.com/models/56519/negativehand-negative-embedding", "save_path": "embeddings/SD1.5", "size": "25KB", "type": "embedding", "url": "https://civitai.com/api/download/models/60938"}, {"base": "SD1.5", "description": "The idea behind this embedding was to somehow train the negative prompt as an embedding, thus unifying the basis of the negative prompt into one word or embedding.", "filename": "bad_prompt_version2-neg.pt", "name": "bad_prompt Negative Embedding", "reference": "https://civitai.com/models/55700/badprompt-negative-embedding", "save_path": "embeddings/SD1.5", "size": "25KB", "type": "embedding", "url": "https://civitai.com/api/download/models/60095"}, {"base": "SD1.5", "description": "These embedding learn what disgusting compositions and color patterns are, including faulty human anatomy, offensive color schemes, upside-down spatial structures, and more. Placing it in the negative can go a long way to avoiding these things.", "filename": "ng_deepnegative_v1_75t.pt", "name": "Deep Negative V1.75", "reference": "https://civitai.com/models/4629/deep-negative-v1x", "save_path": "embeddings/SD1.5", "size": "226KB", "type": "embedding", "url": "https://civitai.com/api/download/models/5637"}, {"base": "SD1.5", "description": "This embedding should be used in your NEGATIVE prompt. Adjust the strength as desired (seems to scale well without any distortions), the strength required may vary based on positive and negative prompts.", "filename": "easynegative.safetensors", "name": "EasyNegative", "reference": "https://civitai.com/models/7808/easynegative", "save_path": "embeddings/SD1.5", "size": "25KB", "type": "embedding", "url": "https://civitai.com/api/download/models/9208"}, {"base": "Stable Cascade", "description": "Stable Cascade stage_b checkpoints", "filename": "stable_cascade_stage_b.safetensors", "name": "stabilityai/comfyui_checkpoints/stable_cascade_stage_b.safetensors", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "checkpoints/Stable-Cascade", "size": "4.55GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/comfyui_checkpoints/stable_cascade_stage_b.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade stage_c checkpoints", "filename": "stable_cascade_stage_c.safetensors", "name": "stabilityai/comfyui_checkpoints/stable_cascade_stage_c.safetensors", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "checkpoints/Stable-Cascade", "size": "9.22GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/comfyui_checkpoints/stable_cascade_stage_c.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: stage_a", "filename": "stage_a.safetensors", "name": "stabilityai/Stable Cascade: stage_a.safetensors (VAE)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "vae/Stable-Cascade", "size": "73.7MB", "type": "VAE", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_a.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: effnet_encoder.\nVAE encoder for stage_c latent.", "filename": "effnet_encoder.safetensors", "name": "stabilityai/Stable Cascade: effnet_encoder.safetensors (VAE)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "vae/Stable-Cascade", "size": "81.5MB", "type": "VAE", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/effnet_encoder.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: stage_b", "filename": "stage_b.safetensors", "name": "stabilityai/Stable Cascade: stage_b.safetensors (UNET)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "diffusion_models/Stable-Cascade", "size": "6.25GB", "type": "diffusion_model", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_b.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: stage_b/bf16", "filename": "stage_b_bf16.safetensors", "name": "stabilityai/Stable Cascade: stage_b_bf16.safetensors (UNET)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "diffusion_models/Stable-Cascade", "size": "3.13GB", "type": "diffusion_model", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_b_bf16.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: stage_b/lite", "filename": "stage_b_lite.safetensors", "name": "stabilityai/Stable Cascade: stage_b_lite.safetensors (UNET)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "diffusion_models/Stable-Cascade", "size": "2.80GB", "type": "diffusion_model", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_b_lite.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: stage_b/bf16,lite", "filename": "stage_b_lite_bf16.safetensors", "name": "stabilityai/Stable Cascade: stage_b_lite.safetensors (UNET)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "diffusion_models/Stable-Cascade", "size": "1.40GB", "type": "diffusion_model", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_b_lite_bf16.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: stage_c", "filename": "stage_c.safetensors", "name": "stabilityai/Stable Cascade: stage_c.safetensors (UNET)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "diffusion_models/Stable-Cascade", "size": "14.4GB", "type": "diffusion_model", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_c.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: stage_c/bf16", "filename": "stage_c_bf16.safetensors", "name": "stabilityai/Stable Cascade: stage_c_bf16.safetensors (UNET)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "diffusion_models/Stable-Cascade", "size": "7.18GB", "type": "diffusion_model", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_c_bf16.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: stage_c/lite", "filename": "stage_c_lite.safetensors", "name": "stabilityai/Stable Cascade: stage_c_lite.safetensors (UNET)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "diffusion_models/Stable-Cascade", "size": "4.12GB", "type": "diffusion_model", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_c_lite.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: stage_c/bf16,lite", "filename": "stage_c_lite_bf16.safetensors", "name": "stabilityai/Stable Cascade: stage_c_lite.safetensors (UNET)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "diffusion_models/Stable-Cascade", "size": "2.06GB", "type": "diffusion_model", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_c_lite_bf16.safetensors"}, {"base": "Stable Cascade", "description": "Stable Cascade: text_encoder", "filename": "model.safetensors", "name": "stabilityai/Stable Cascade: text_encoder (CLIP)", "reference": "https://huggingface.co/stabilityai/stable-cascade", "save_path": "text_encoders/Stable-Cascade", "size": "1.39GB", "type": "clip", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/text_encoder/model.safetensors"}, {"base": "SDXL", "description": "SDXL-Turbo 1.0 fp16", "filename": "sd_xl_turbo_1.0_fp16.safetensors", "name": "SDXL-Turbo 1.0 (fp16)", "reference": "https://huggingface.co/stabilityai/sdxl-turbo", "save_path": "checkpoints/SDXL-TURBO", "size": "6.94GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/sdxl-turbo/resolve/main/sd_xl_turbo_1.0_fp16.safetensors"}, {"base": "SDXL", "description": "SDXL-Turbo 1.0", "filename": "sd_xl_turbo_1.0.safetensors", "name": "SDXL-Turbo 1.0", "reference": "https://huggingface.co/stabilityai/sdxl-turbo", "save_path": "checkpoints/SDXL-TURBO", "size": "13.9GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/sdxl-turbo/resolve/main/sd_xl_turbo_1.0.safetensors"}, {"base": "SDXL", "description": "Stable Diffusion XL base model (VAE 0.9)", "filename": "sd_xl_base_1.0_0.9vae.safetensors", "name": "sd_xl_base_1.0_0.9vae.safetensors", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0", "save_path": "checkpoints/SDXL", "size": "6.94GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0_0.9vae.safetensors"}, {"base": "SDXL", "description": "Stable Diffusion XL base model", "filename": "sd_xl_base_1.0.safetensors", "name": "sd_xl_base_1.0.safetensors", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0", "save_path": "checkpoints/SDXL", "size": "6.94GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors"}, {"base": "SDXL", "description": "Stable Diffusion XL refiner model (VAE 0.9)", "filename": "sd_xl_refiner_1.0_0.9vae.safetensors", "name": "sd_xl_refiner_1.0_0.9vae.safetensors", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0", "save_path": "checkpoints/SDXL", "size": "6.08GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0_0.9vae.safetensors"}, {"base": "SDXL", "description": "Stable Diffusion XL refiner model", "filename": "sd_xl_refiner_1.0.safetensors", "name": "stable-diffusion-xl-refiner-1.0", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0", "save_path": "checkpoints/SDXL", "size": "6.08GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0.safetensors"}, {"base": "SDXL", "description": "Stable Diffusion XL inpainting model 0.1. You need UNETLoader instead of CheckpointLoader.", "filename": "diffusion_pytorch_model.fp16.safetensors", "name": "diffusers/stable-diffusion-xl-1.0-inpainting-0.1 (diffusion_models/fp16)", "reference": "https://huggingface.co/diffusers/stable-diffusion-xl-1.0-inpainting-0.1", "save_path": "diffusion_models/xl-inpaint-0.1", "size": "5.14GB", "type": "diffusion_model", "url": "https://huggingface.co/diffusers/stable-diffusion-xl-1.0-inpainting-0.1/resolve/main/unet/diffusion_pytorch_model.fp16.safetensors"}, {"base": "SDXL", "description": "Stable Diffusion XL inpainting model 0.1. You need UNETLoader instead of CheckpointLoader.", "filename": "diffusion_pytorch_model.safetensors", "name": "diffusers/stable-diffusion-xl-1.0-inpainting-0.1 (UNET)", "reference": "https://huggingface.co/diffusers/stable-diffusion-xl-1.0-inpainting-0.1", "save_path": "diffusion_models/xl-inpaint-0.1", "size": "10.3GB", "type": "diffusion_model", "url": "https://huggingface.co/diffusers/stable-diffusion-xl-1.0-inpainting-0.1/resolve/main/unet/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "Stable Diffusion XL offset LoRA", "filename": "sd_xl_offset_example-lora_1.0.safetensors", "name": "sd_xl_offset_example-lora_1.0.safetensors", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0", "save_path": "loras/SDXL", "size": "49.6MB", "type": "lora", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_offset_example-lora_1.0.safetensors"}, {"base": "SDXL", "description": "SDXL Lightning LoRA (2steps)", "filename": "sdxl_lightning_2step_lora.safetensors", "name": "SDXL Lightning LoRA (2steps)", "reference": "https://huggingface.co/ByteDance/SDXL-Lightning", "save_path": "loras/SDXL-Lightning", "size": "393.9MB", "type": "lora", "url": "https://huggingface.co/ByteDance/SDXL-Lightning/resolve/main/sdxl_lightning_2step_lora.safetensors"}, {"base": "SDXL", "description": "SDXL Lightning LoRA (4steps)", "filename": "sdxl_lightning_4step_lora.safetensors", "name": "SDXL Lightning LoRA (4steps)", "reference": "https://huggingface.co/ByteDance/SDXL-Lightning", "save_path": "loras/SDXL-Lightning", "size": "393.9MB", "type": "lora", "url": "https://huggingface.co/ByteDance/SDXL-Lightning/resolve/main/sdxl_lightning_4step_lora.safetensors"}, {"base": "SDXL", "description": "SDXL Lightning LoRA (8steps)", "filename": "sdxl_lightning_8step_lora.safetensors", "name": "SDXL Lightning LoRA (8steps)", "reference": "https://huggingface.co/ByteDance/SDXL-Lightning", "save_path": "loras/SDXL-Lightning", "size": "393.9MB", "type": "lora", "url": "https://huggingface.co/ByteDance/SDXL-Lightning/resolve/main/sdxl_lightning_8step_lora.safetensors"}, {"base": "SDXL", "description": "DMD2 LoRA (4steps)", "filename": "dmd2_sdxl_4step_lora.safetensors", "name": "DMD2 LoRA (4steps)", "reference": "https://huggingface.co/tianweiy/DMD2", "save_path": "loras/DMD2", "size": "787MB", "type": "lora", "url": "https://huggingface.co/tianweiy/DMD2/resolve/main/dmd2_sdxl_4step_lora.safetensors"}, {"base": "SDXL", "description": "DMD2 LoRA (4steps/fp16)", "filename": "dmd2_sdxl_4step_lora_fp16.safetensors", "name": "DMD2 LoRA (4steps/fp16)", "reference": "https://huggingface.co/tianweiy/DMD2", "save_path": "loras/DMD2", "size": "394MB", "type": "lora", "url": "https://huggingface.co/tianweiy/DMD2/resolve/main/dmd2_sdxl_4step_lora_fp16.safetensors"}, {"base": "FLUX.1", "description": "Hyper-SD LoRA (8steps) - FLUX.1 [Dev]", "filename": "Hyper-FLUX.1-dev-8steps-lora.safetensors", "name": "Hyper-SD LoRA (8steps) - FLUX.1 [Dev]", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/FLUX.1", "size": "1.39GB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-FLUX.1-dev-8steps-lora.safetensors"}, {"base": "FLUX.1", "description": "Hyper-SD LoRA (16steps) - FLUX.1 [Dev]", "filename": "Hyper-FLUX.1-dev-16steps-lora.safetensors", "name": "Hyper-SD LoRA (16steps) - FLUX.1 [Dev]", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/FLUX.1", "size": "1.39GB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-FLUX.1-dev-16steps-lora.safetensors"}, {"base": "SD1.5", "description": "Hyper-SD LoRA (1step) - SD1.5", "filename": "Hyper-SD15-1step-lora.safetensors", "name": "Hyper-SD LoRA (1step) - SD1.5", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SD15", "size": "269MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-1step-lora.safetensors"}, {"base": "SD1.5", "description": "Hyper-SD LoRA (2steps) - SD1.5", "filename": "Hyper-SD15-2steps-lora.safetensors", "name": "Hyper-SD LoRA (2steps) - SD1.5", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SD15", "size": "269MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-2steps-lora.safetensors"}, {"base": "SD1.5", "description": "Hyper-SD LoRA (4steps)", "filename": "Hyper-SD15-4steps-lora.safetensors", "name": "Hyper-SD LoRA (4steps) - SD1.5", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SD15", "size": "269MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-4steps-lora.safetensors"}, {"base": "SD1.5", "description": "Hyper-SD LoRA (8steps)", "filename": "Hyper-SD15-8steps-lora.safetensors", "name": "Hyper-SD LoRA (8steps) - SD1.5", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SD15", "size": "269MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-8steps-lora.safetensors"}, {"base": "SD1.5", "description": "Hyper-SD CFG LoRA (8steps)", "filename": "Hyper-SD15-8steps-CFG-lora.safetensors", "name": "Hyper-SD CFG LoRA (8steps) - SD1.5", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SD15", "size": "269MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-8steps-CFG-lora.safetensors"}, {"base": "SD1.5", "description": "Hyper-SD CFG LoRA (12steps)", "filename": "Hyper-SD15-12steps-CFG-lora.safetensors", "name": "Hyper-SD CFG LoRA (12steps) - SD1.5", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SD15", "size": "269MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-12steps-CFG-lora.safetensors"}, {"base": "SDXL", "description": "Hyper-SD LoRA (1step) - SDXL", "filename": "Hyper-SDXL-1step-lora.safetensors", "name": "Hyper-SD LoRA (1step) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SDXL", "size": "787MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-1step-lora.safetensors"}, {"base": "SDXL", "description": "Hyper-SD LoRA (2steps) - SDXL", "filename": "Hyper-SDXL-2steps-lora.safetensors", "name": "Hyper-SD LoRA (2steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SDXL", "size": "787MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-2steps-lora.safetensors"}, {"base": "SDXL", "description": "Hyper-SD LoRA (4steps) - SDXL", "filename": "Hyper-SDXL-4steps-lora.safetensors", "name": "Hyper-SD LoRA (4steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SDXL", "size": "787MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-4steps-lora.safetensors"}, {"base": "SDXL", "description": "Hyper-SD LoRA (8steps) - SDXL", "filename": "Hyper-SDXL-8steps-lora.safetensors", "name": "Hyper-SD LoRA (8steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SDXL", "size": "787MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-8steps-lora.safetensors"}, {"base": "SDXL", "description": "Hyper-SD CFG LoRA (8steps) - SDXL", "filename": "Hyper-SDXL-8steps-CFG-lora.safetensors", "name": "Hyper-SD CFG LoRA (8steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SDXL", "size": "787MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-8steps-CFG-lora.safetensors"}, {"base": "SDXL", "description": "Hyper-SD CFG LoRA (12steps) - SDXL", "filename": "Hyper-SDXL-12steps-CFG-lora.safetensors", "name": "Hyper-SD CFG LoRA (12steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SDXL", "size": "787MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-12steps-CFG-lora.safetensors"}, {"base": "SD3", "description": "Hyper-SD CFG LoRA (4steps) - SD3", "filename": "Hyper-SD3-4steps-CFG-lora.safetensors", "name": "Hyper-SD CFG LoRA (4steps) - SD3", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SD3", "size": "472MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD3-4steps-CFG-lora.safetensors"}, {"base": "SD3", "description": "Hyper-SD CFG LoRA (8steps) - SD3", "filename": "Hyper-SD3-8steps-CFG-lora.safetensors", "name": "Hyper-SD CFG LoRA (8steps) - SD3", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SD3", "size": "472MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD3-8steps-CFG-lora.safetensors"}, {"base": "SD3", "description": "Hyper-SD CFG LoRA (16steps) - SD3", "filename": "Hyper-SD3-16steps-CFG-lora.safetensors", "name": "Hyper-SD CFG LoRA (16steps) - SD3", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "save_path": "loras/HyperSD/SD3", "size": "472MB", "type": "lora", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD3-16steps-CFG-lora.safetensors"}, {"base": "t5", "description": "Text Encoders for FLUX (fp16)", "filename": "t5xxl_fp16.safetensors", "name": "comfyanonymous/flux_text_encoders - t5xxl (fp16)", "reference": "https://huggingface.co/comfyanonymous/flux_text_encoders", "save_path": "text_encoders/t5", "size": "9.79GB", "type": "clip", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors"}, {"base": "t5", "description": "Text Encoders for FLUX (fp8_e4m3fn)", "filename": "t5xxl_fp8_e4m3fn.safetensors", "name": "comfyanonymous/flux_text_encoders - t5xxl (fp8_e4m3fn)", "reference": "https://huggingface.co/comfyanonymous/flux_text_encoders", "save_path": "text_encoders/t5", "size": "4.89GB", "type": "clip", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn.safetensors"}, {"base": "t5", "description": "Text Encoders for FLUX (fp16)", "filename": "t5xxl_fp8_e4m3fn_scaled.safetensors", "name": "comfyanonymous/flux_text_encoders - t5xxl (fp8_e4m3fn_scaled)", "reference": "https://huggingface.co/comfyanonymous/flux_text_encoders", "save_path": "text_encoders/t5", "size": "5.16GB", "type": "clip", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors"}, {"base": "Cosmos-1.0", "description": "VAE model for Cosmos 1.0", "filename": "cosmos_cv8x8x8_1.0.safetensors", "name": "comfyanonymous/cosmos_cv8x8x8_1.0.safetensors", "reference": "https://huggingface.co/comfyanonymous/cosmos_1.0_text_encoder_and_VAE_ComfyUI/tree/main", "save_path": "default", "size": "211MB", "type": "VAE", "url": "https://huggingface.co/comfyanonymous/cosmos_1.0_text_encoder_and_VAE_ComfyUI/resolve/main/vae/cosmos_cv8x8x8_1.0.safetensors"}, {"base": "Cosmos-1.0", "description": "Cosmos 1.0 Text2World Diffusion Model (7B)", "filename": "Cosmos-1_0-Diffusion-7B-Text2World.safetensors", "name": "mcmonkey/Cosmos-1_0-Diffusion-7B-Text2World.safetensors", "reference": "https://huggingface.co/mcmonkey/cosmos-1.0", "save_path": "diffusion_models/cosmos-1.0", "size": "14.5GB", "type": "diffusion_model", "url": "https://huggingface.co/mcmonkey/cosmos-1.0/resolve/main/Cosmos-1_0-Diffusion-7B-Text2World.safetensors"}, {"base": "Cosmos-1.0", "description": "Cosmos 1.0 Video2World Diffusion Model (7B)", "filename": "Cosmos-1_0-Diffusion-7B-Video2World.safetensors", "name": "mcmonkey/Cosmos-1_0-Diffusion-7B-Video2World.safetensors", "reference": "https://huggingface.co/mcmonkey/cosmos-1.0", "save_path": "diffusion_models/cosmos-1.0", "size": "14.5GB", "type": "diffusion_model", "url": "https://huggingface.co/mcmonkey/cosmos-1.0/resolve/main/Cosmos-1_0-Diffusion-7B-Video2World.safetensors"}, {"base": "Cosmos-1.0", "description": "Cosmos 1.0 Text2World Diffusion Model (14B)", "filename": "Cosmos-1_0-Diffusion-14B-Text2World.safetensors", "name": "mcmonkey/Cosmos-1_0-Diffusion-14B-Text2World.safetensors", "reference": "https://huggingface.co/mcmonkey/cosmos-1.0", "save_path": "diffusion_models/cosmos-1.0", "size": "28.5GB", "type": "diffusion_model", "url": "https://huggingface.co/mcmonkey/cosmos-1.0/resolve/main/Cosmos-1_0-Diffusion-14B-Text2World.safetensors"}, {"base": "Cosmos-1.0", "description": "Cosmos 1.0 Video2World Diffusion Model (14B)", "filename": "Cosmos-1_0-Diffusion-14B-Video2World.safetensors", "name": "mcmonkey/Cosmos-1_0-Diffusion-14B-Video2World.safetensors", "reference": "https://huggingface.co/mcmonkey/cosmos-1.0", "save_path": "diffusion_models/cosmos-1.0", "size": "28.5GB", "type": "diffusion_model", "url": "https://huggingface.co/mcmonkey/cosmos-1.0/resolve/main/Cosmos-1_0-Diffusion-14B-Video2World.safetensors"}, {"base": "t5-base", "description": "T5 Base: Text-To-Text Transfer Transformer. This model can be loaded via CLIPLoader for Stable Audio workflow.", "filename": "model.safetensors", "name": "google-t5/t5-base", "reference": "https://huggingface.co/google-t5/t5-base", "save_path": "text_encoders/t5-base", "size": "892MB", "type": "clip", "url": "https://huggingface.co/google-t5/t5-base/resolve/main/model.safetensors"}, {"base": "t5", "description": "The encoder part of https://huggingface.co/google/t5-v1_1-xxl, used with SD3 and Flux1", "filename": "google_t5-v1_1-xxl_encoderonly-fp16.safetensors", "name": "google-t5/t5-v1_1-xxl_encoderonly-fp16", "reference": "https://huggingface.co/mcmonkey/google_t5-v1_1-xxl_encoderonly", "save_path": "text_encoders/t5", "size": "10.1GB", "type": "clip", "url": "https://huggingface.co/mcmonkey/google_t5-v1_1-xxl_encoderonly/resolve/main/model.safetensors"}, {"base": "t5", "description": "The encoder part of https://huggingface.co/google/t5-v1_1-xxl, used with SD3 and Flux1", "filename": "google_t5-v1_1-xxl_encoderonly-fp8_e4m3fn.safetensors", "name": "google-t5/t5-v1_1-xxl_encoderonly-fp8_e4m3fn", "reference": "https://huggingface.co/mcmonkey/google_t5-v1_1-xxl_encoderonly", "save_path": "text_encoders/t5", "size": "4.89GB", "type": "clip", "url": "https://huggingface.co/mcmonkey/google_t5-v1_1-xxl_encoderonly/resolve/main/t5xxl_fp8_e4m3fn.safetensors"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (Q3_K_L quantized)", "filename": "t5-v1_1-xxl-encoder-Q3_K_L.gguf", "name": "city96/t5-v1_1-xxl-encoder-Q3_K_L.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "2.46GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q3_K_L.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (Q3_K_M quantized)", "filename": "t5-v1_1-xxl-encoder-Q3_K_M.gguf", "name": "city96/t5-v1_1-xxl-encoder-Q3_K_M.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "2.3GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q3_K_M.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (Q3_K_S quantized)", "filename": "t5-v1_1-xxl-encoder-Q3_K_S.gguf", "name": "city96/t5-v1_1-xxl-encoder-Q3_K_S.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "2.1GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q3_K_S.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (Q4_K_M quantized)", "filename": "t5-v1_1-xxl-encoder-Q4_K_M.gguf", "name": "city96/t5-v1_1-xxl-encoder-Q4_K_M.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "2.9GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q4_K_M.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (Q4_K_S quantized)", "filename": "t5-v1_1-xxl-encoder-Q4_K_S.gguf", "name": "city96/t5-v1_1-xxl-encoder-Q4_K_S.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "2.74GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q4_K_S.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (Q5_K_M quantized)", "filename": "t5-v1_1-xxl-encoder-Q5_K_M.gguf", "name": "city96/t5-v1_1-xxl-encoder-Q5_K_M.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "3.39GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q5_K_M.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (Q5_K_S quantized)", "filename": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "name": "city96/t5-v1_1-xxl-encoder-Q5_K_S.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "3.29GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q5_K_S.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (Q6_K quantized)", "filename": "t5-v1_1-xxl-encoder-Q6_K.gguf", "name": "city96/t5-v1_1-xxl-encoder-Q6_K.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "3.91GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q6_K.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (Q8_0 quantized)", "filename": "t5-v1_1-xxl-encoder-Q8_0.gguf", "name": "city96/t5-v1_1-xxl-encoder-Q8_0.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "5.06GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q8_0.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (float 16)", "filename": "t5-v1_1-xxl-encoder-f16.gguf", "name": "city96/t5-v1_1-xxl-encoder-f16.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "9.53GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-f16.gguf"}, {"base": "t5", "description": "t5xxl Text Encoder GGUF model. (float 32)", "filename": "t5-v1_1-xxl-encoder-f32.gguf", "name": "city96/t5-v1_1-xxl-encoder-f32.gguf", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "save_path": "text_encoders/t5", "size": "19.1GB", "type": "clip", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-f32.gguf"}, {"base": "clip", "description": "clip_l model (for SD1.x, SD2.x, SDXL, SD3.5, FLUX.1, Hu<PERSON>uanVideo, ...) ", "filename": "clip_l.safetensors", "name": "Comfy-Org/clip_l", "reference": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8", "save_path": "default", "size": "246MB", "type": "clip", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8/resolve/main/text_encoders/clip_l.safetensors"}, {"base": "clip", "description": "clip_g model (for SDXL, SD3.5)", "filename": "clip_g.safetensors", "name": "Comfy-Org/clip_g", "reference": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8", "save_path": "default", "size": "1.39GB", "type": "clip", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8/resolve/main/text_encoders/clip_g.safetensors"}, {"base": "SD1.5", "description": "Stable Diffusion 1.5 base model", "filename": "v1-5-pruned-emaonly.ckpt", "name": "v1-5-pruned-emaonly.ckpt", "reference": "https://huggingface.co/runwayml/stable-diffusion-v1-5", "save_path": "checkpoints/SD1.5", "size": "4.27GB", "type": "checkpoint", "url": "https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.ckpt"}, {"base": "SD2", "description": "Stable Diffusion 2 base model (512)", "filename": "v2-1_512-ema-pruned.safetensors", "name": "v2-1_512-ema-pruned.safetensors", "reference": "https://huggingface.co/stabilityai/stable-diffusion-2-1-base", "save_path": "checkpoints/SD2.1", "size": "5.21GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-1-base/resolve/main/v2-1_512-ema-pruned.safetensors"}, {"base": "SD2", "description": "Stable Diffusion 2 base model (768)", "filename": "v2-1_768-ema-pruned.safetensors", "name": "v2-1_768-ema-pruned.safetensors", "reference": "https://huggingface.co/stabilityai/stable-diffusion-2-1", "save_path": "checkpoints/SD2.1", "size": "5.21GB", "type": "checkpoint", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-1/resolve/main/v2-1_768-ema-pruned.safetensors"}, {"base": "SD1.5", "description": "AbyssOrangeMix2 - hard version (anime style)", "filename": "AbyssOrangeMix2_hard.safetensors", "name": "AbyssOrangeMix2 (hard)", "reference": "https://huggingface.co/WarriorMama777/OrangeMixs", "save_path": "checkpoints/SD1.5", "size": "5.57GB", "type": "checkpoint", "url": "https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix2/AbyssOrangeMix2_hard.safetensors"}, {"base": "SD1.5", "description": "AbyssOrangeMix3 - A1 (anime style)", "filename": "AOM3A1_orangemixs.safetensors", "name": "AbyssOrangeMix3 A1", "reference": "https://huggingface.co/WarriorMama777/OrangeMixs", "save_path": "checkpoints/SD1.5", "size": "2.13GB", "type": "checkpoint", "url": "https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix3/AOM3A1_orangemixs.safetensors"}, {"base": "SD1.5", "description": "AbyssOrangeMix - A3 (anime style)", "filename": "AOM3A3_orangemixs.safetensors", "name": "AbyssOrangeMix3 A3", "reference": "https://huggingface.co/WarriorMama777/OrangeMixs", "save_path": "checkpoints/SD1.5", "size": "2.13GB", "type": "checkpoint", "url": "https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix3/AOM3A3_orangemixs.safetensors"}, {"base": "SD2.1", "description": "Waifu Diffusion 1.5 Beta3", "filename": "wd-illusion-fp16.safetensors", "name": "Waifu Diffusion 1.5 Beta3 (fp16)", "reference": "https://huggingface.co/waifu-diffusion/wd-1-5-beta3", "save_path": "checkpoints/SD2.1", "size": "2.58GB", "type": "checkpoint", "url": "https://huggingface.co/waifu-diffusion/wd-1-5-beta3/resolve/main/wd-illusion-fp16.safetensors"}, {"base": "SD2.1", "description": "Mix model (SD2.1 unCLIP + illuminatiDiffusionV1_v11)", "filename": "illuminatiDiffusionV1_v11-unclip-h-fp16.safetensors", "name": "illuminatiDiffusionV1_v11 unCLIP model", "reference": "https://huggingface.co/comfyanonymous/illuminatiDiffusionV1_v11_unCLIP", "save_path": "checkpoints/SD2.1", "size": "3.98GB", "type": "unclip", "url": "https://huggingface.co/comfyanonymous/illuminatiDiffusionV1_v11_unCLIP/resolve/main/illuminatiDiffusionV1_v11-unclip-h-fp16.safetensors"}, {"base": "SD2.1", "description": "Mix model (SD2.1 unCLIP + Waifu Diffusion 1.5)", "filename": "wd-1-5-beta2-aesthetic-unclip-h-fp16.safetensors", "name": "Waifu Diffusion 1.5 unCLIP model", "reference": "https://huggingface.co/comfyanonymous/wd-1.5-beta2_unCLIP", "save_path": "checkpoints/SD2.1", "size": "3.98GB", "type": "unclip", "url": "https://huggingface.co/comfyanonymous/wd-1.5-beta2_unCLIP/resolve/main/wd-1-5-beta2-aesthetic-unclip-h-fp16.safetensors"}, {"base": "SDXL", "description": "SDXL-VAE", "filename": "sdxl_vae.safetensors", "name": "sdxl_vae.safetensors", "reference": "https://huggingface.co/stabilityai/sdxl-vae", "save_path": "vae/SDXL", "size": "334.6MB", "type": "VAE", "url": "https://huggingface.co/stabilityai/sdxl-vae/resolve/main/sdxl_vae.safetensors"}, {"base": "SD1.5", "description": "vae-ft-mse-840000-ema-pruned", "filename": "vae-ft-mse-840000-ema-pruned.safetensors", "name": "vae-ft-mse-840000-ema-pruned", "reference": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original", "save_path": "vae/SD1.5", "size": "334.6MB", "type": "VAE", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors"}, {"base": "SD1.5", "description": "orangemix vae model", "filename": "orangemix.vae.pt", "name": "orangemix.vae", "reference": "https://huggingface.co/WarriorMama777/OrangeMixs", "save_path": "vae/SD1.5", "size": "822.8MB", "type": "VAE", "url": "https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/VAEs/orangemix.vae.pt"}, {"base": "SD2.1", "description": "kl-f8-anime2 vae model", "filename": "kl-f8-anime2.ckpt", "name": "kl-f8-anime2", "reference": "https://huggingface.co/hakurei/waifu-diffusion-v1-4", "save_path": "vae/SD2.1", "size": "404.7MB", "type": "VAE", "url": "https://huggingface.co/hakurei/waifu-diffusion-v1-4/resolve/main/vae/kl-f8-anime2.ckpt"}, {"base": "SD1.5", "description": "OpenAI Consistency Decoder. Improved decoding for stable diffusion vaes.", "filename": "decoder.pt", "name": "OpenAI Consistency Decoder", "reference": "https://github.com/openai/consistencydecoder", "save_path": "vae/SD1.5/openai_consistency_decoder", "size": "2.49GB", "type": "VAE", "url": "https://openaipublic.azureedge.net/diff-vae/c9cebd3132dd9c42936d803e33424145a748843c8f716c0814838bdc8a2fe7cb/decoder.pt"}, {"base": "SD1.5", "description": "Latent Consistency LoRA for SD1.5", "filename": "pytorch_lora_weights.safetensors", "name": "LCM LoRA SD1.5", "reference": "https://huggingface.co/latent-consistency/lcm-lora-sdv1-5", "save_path": "loras/SD1.5/lcm", "size": "134.6MB", "type": "lora", "url": "https://huggingface.co/latent-consistency/lcm-lora-sdv1-5/resolve/main/pytorch_lora_weights.safetensors"}, {"base": "SSD-1B", "description": "Latent Consistency LoRA for SSD-1B", "filename": "pytorch_lora_weights.safetensors", "name": "LCM LoRA SSD-1B", "reference": "https://huggingface.co/latent-consistency/lcm-lora-ssd-1b", "save_path": "loras/SSD-1B/lcm", "size": "210.0MB", "type": "lora", "url": "https://huggingface.co/latent-consistency/lcm-lora-ssd-1b/resolve/main/pytorch_lora_weights.safetensors"}, {"base": "SDXL", "description": "Latent Consistency LoRA for SDXL", "filename": "pytorch_lora_weights.safetensors", "name": "LCM LoRA SDXL", "reference": "https://huggingface.co/latent-consistency/lcm-lora-sdxl", "save_path": "loras/SDXL/lcm", "size": "393.9MB", "type": "lora", "url": "https://huggingface.co/latent-consistency/lcm-lora-sdxl/resolve/main/pytorch_lora_weights.safetensors"}, {"base": "segmind-vega", "description": "The Segmind-Vega Model is a distilled version of the Stable Diffusion XL (SDXL), offering a remarkable 70% reduction in size and an impressive 100% speedup while retaining high-quality text-to-image generation capabilities.", "filename": "segmind-vega.safetensors", "name": "Segmind<PERSON>Vega", "reference": "https://huggingface.co/segmind/Segmind-<PERSON>", "save_path": "checkpoints/segmind-vega", "size": "3.29GB", "type": "checkpoint", "url": "https://huggingface.co/segmind/Segmind-Vega/resolve/main/segmind-vega.safetensors"}, {"base": "segmind-vega", "description": "Segmind-VegaRT a distilled consistency adapter for Segmind-Vega that allows to reduce the number of inference steps to only between 2 - 8 steps.", "filename": "pytorch_lora_weights.safetensors", "name": "Segmind-VegaRT - Latent Consistency Model (LCM) LoRA of Segmind-Vega", "reference": "https://huggingface.co/segmind/Segmind-VegaRT", "save_path": "loras/segmind-vega", "size": "239.2MB", "type": "lora", "url": "https://huggingface.co/segmind/Segmind-VegaRT/resolve/main/pytorch_lora_weights.safetensors"}, {"base": "SD2.1", "description": "LORA: Theovercomer8's Contrast Fix (SD2.1)", "filename": "theovercomer8sContrastFix_sd21768.safetensors", "name": "Theovercomer8's Contrast Fix (SD2.1)", "reference": "https://civitai.com/models/8765/theovercomer8s-contrast-fix-sd15sd21-768", "save_path": "loras/SD2.1", "size": "163MB", "type": "lora", "url": "https://civitai.com/api/download/models/10350"}, {"base": "SD1.5", "description": "LORA: Theovercomer8's Contrast Fix (SD1.5)", "filename": "theovercomer8sContrastFix_sd15.safetensors", "name": "Theovercomer8's Contrast Fix (SD1.5)", "reference": "https://civitai.com/models/8765/theovercomer8s-contrast-fix-sd15sd21-768", "save_path": "loras/SD1.5", "size": "113MB", "type": "lora", "url": "https://civitai.com/api/download/models/10638"}, {"base": "SD1.5", "description": "ControlNet T2I-Adapter for depth", "filename": "t2iadapter_depth_sd14v1.pth", "name": "T2I-Adapter (depth)", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "save_path": "controlnet/SD1.5", "size": "309.5MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_depth_sd14v1.pth"}, {"base": "SD1.5", "description": "ControlNet T2I-Adapter for seg", "filename": "t2iadapter_seg_sd14v1.pth", "name": "T2I-Adapter (seg)", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "save_path": "controlnet/SD1.5", "size": "309.5MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_seg_sd14v1.pth"}, {"base": "SD1.5", "description": "ControlNet T2I-Adapter for sketch", "filename": "t2iadapter_sketch_sd14v1.pth", "name": "T2I-Adapter (sketch)", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "save_path": "controlnet/SD1.5", "size": "308.0MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_sketch_sd14v1.pth"}, {"base": "SD1.5", "description": "ControlNet T2I-Adapter for keypose", "filename": "t2iadapter_keypose_sd14v1.pth", "name": "T2I-Adapter (keypose)", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "save_path": "controlnet/SD1.5", "size": "309.5MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_keypose_sd14v1.pth"}, {"base": "SD1.5", "description": "ControlNet T2I-Adapter for openpose", "filename": "t2iadapter_openpose_sd14v1.pth", "name": "T2I-Adapter (openpose)", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "save_path": "controlnet/SD1.5", "size": "309.5MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_openpose_sd14v1.pth"}, {"base": "SD1.5", "description": "ControlNet T2I-Adapter for color", "filename": "t2iadapter_color_sd14v1.pth", "name": "T2I-Adapter (color)", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "save_path": "controlnet/SD1.5", "size": "74.8MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_color_sd14v1.pth"}, {"base": "SD1.5", "description": "ControlNet T2I-Adapter for canny", "filename": "t2iadapter_canny_sd14v1.pth", "name": "T2I-Adapter (canny)", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "save_path": "controlnet/SD1.5", "size": "308.0MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_canny_sd14v1.pth"}, {"base": "SD1.5", "description": "ControlNet T2I-Adapter style model. Need to download CLIPVision model.", "filename": "t2iadapter_style_sd14v1.pth", "name": "T2I-Style model", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "save_path": "controlnet/SD1.5", "size": "154.4MB", "type": "T2I-Style", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_style_sd14v1.pth"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for lineart", "filename": "t2i-adapter-lineart-sdxl-1.0.fp16.safetensors", "name": "T2I-Adapter XL (lineart) FP16", "reference": "https://huggingface.co/TencentARC/t2i-adapter-lineart-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "158.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-lineart-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for canny", "filename": "t2i-adapter-canny-sdxl-1.0.fp16.safetensors", "name": "T2I-Adapter XL (canny) FP16", "reference": "https://huggingface.co/TencentARC/t2i-adapter-canny-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "158.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-canny-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for depth-zoe", "filename": "t2i-adapter-depth-zoe-sdxl-1.0.fp16.safetensors", "name": "T2I-Adapter XL (depth-zoe) FP16", "reference": "https://huggingface.co/TencentARC/t2i-adapter-depth-zoe-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "158.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-depth-zoe-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for depth-midas", "filename": "t2i-adapter-depth-midas-sdxl-1.0.fp16.safetensors", "name": "T2I-Adapter XL (depth-midas) FP16", "reference": "https://huggingface.co/TencentARC/t2i-adapter-depth-midas-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "158.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-depth-midas-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for sketch", "filename": "t2i-adapter-sketch-sdxl-1.0.fp16.safetensors", "name": "T2I-Adapter XL (sketch) FP16", "reference": "https://huggingface.co/TencentARC/t2i-adapter-sketch-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "158.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-sketch-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for lineart", "filename": "t2i-adapter-lineart-sdxl-1.0.safetensors", "name": "T2I-Adapter XL (lineart)", "reference": "https://huggingface.co/TencentARC/t2i-adapter-lineart-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "316.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-lineart-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for canny", "filename": "t2i-adapter-canny-sdxl-1.0.safetensors", "name": "T2I-Adapter XL (canny)", "reference": "https://huggingface.co/TencentARC/t2i-adapter-canny-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "316.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-canny-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for depth-zoe", "filename": "t2i-adapter-depth-zoe-sdxl-1.0.safetensors", "name": "T2I-Adapter XL (depth-zoe)", "reference": "https://huggingface.co/TencentARC/t2i-adapter-depth-zoe-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "316.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-depth-zoe-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for depth-midas", "filename": "t2i-adapter-depth-midas-sdxl-1.0.safetensors", "name": "T2I-Adapter XL (depth-midas)", "reference": "https://huggingface.co/TencentARC/t2i-adapter-depth-midas-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "316.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-depth-midas-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for sketch", "filename": "t2i-adapter-sketch-sdxl-1.0.safetensors", "name": "T2I-Adapter XL (sketch)", "reference": "https://huggingface.co/TencentARC/t2i-adapter-sketch-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "316.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-sketch-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "ControlNet T2I-Adapter XL for openpose", "filename": "t2i-adapter-openpose-sdxl-1.0.safetensors", "name": "T2I-Adapter XL (openpose)", "reference": "https://huggingface.co/TencentARC/t2i-adapter-openpose-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "316.1MB", "type": "T2I-Adapter", "url": "https://huggingface.co/TencentARC/t2i-adapter-openpose-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SD1.5", "description": "TemporalNet was a ControlNet model designed to enhance the temporal consistency of generated outputs", "filename": "temporalnetversion2.safetensors", "name": "CiaraRowles/TemporalNet2", "reference": "https://huggingface.co/CiaraRowles/TemporalNet2", "save_path": "controlnet/SD1.5", "size": "5.71GB", "type": "controlnet", "url": "https://huggingface.co/CiaraRowles/TemporalNet2/resolve/main/temporalnetversion2.safetensors"}, {"base": "SDXL", "description": "This is TemporalNet1XL, it is a re-train of the controlnet TemporalNet1 with Stable Diffusion XL.", "filename": "diffusion_pytorch_model.safetensors", "name": "CiaraRowles/TemporalNet1XL (1.0)", "reference": "https://huggingface.co/CiaraRowles/controlnet-temporalnet-sdxl-1.0", "save_path": "controlnet/SDXL/TemporalNet1XL", "size": "5.00GB", "type": "controlnet", "url": "https://huggingface.co/CiaraRowles/controlnet-temporalnet-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "sigclip", "description": "This clip vision model is required for FLUX.1 Redux.", "filename": "sigclip_vision_patch14_384.safetensors", "name": "Comfy-Org/sigclip_vision_384 (patch14_384)", "reference": "https://huggingface.co/Comfy-Org/sigclip_vision_384/tree/main", "save_path": "clip_vision", "size": "857MB", "type": "clip_vision", "url": "https://huggingface.co/Comfy-Org/sigclip_vision_384/resolve/main/sigclip_vision_patch14_384.safetensors"}, {"base": "ViT-G", "description": "clip_g vision model", "filename": "clip_vision_g.safetensors", "name": "CLIPVision model (stabilityai/clip_vision_g)", "reference": "https://huggingface.co/stabilityai/control-lora", "save_path": "clip_vision", "size": "3.69GB", "type": "clip_vision", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/revision/clip_vision_g.safetensors"}, {"base": "ViT-L", "description": "CLIPVision model (needed for styles model)", "filename": "clip-vit-large-patch14.safetensors", "name": "CLIPVision model (openai/clip-vit-large)", "reference": "https://huggingface.co/openai/clip-vit-large-patch14", "save_path": "clip_vision", "size": "1.71GB", "type": "clip_vision", "url": "https://huggingface.co/openai/clip-vit-large-patch14/resolve/main/model.safetensors"}, {"base": "ViT-L", "description": "CLIPVision model (needed for IP-Adapter)", "filename": "clip-vit-large-patch14-336.bin", "name": "CLIPVision model (Kwai-Kolors/Kolors-IP-Adapter-Plus/clip-vit-large)", "reference": "https://huggingface.co/K<PERSON>-Kolors/Kolors-IP-Adapter-Plus", "save_path": "clip_vision", "size": "1.71GB", "type": "clip_vision", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors-IP-Adapter-Plus/resolve/main/image_encoder/pytorch_model.bin"}, {"base": "ViT-H", "description": "CLIPVision model (needed for IP-Adapter)", "filename": "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors", "name": "CLIPVision model (IP-Adapter) CLIP-ViT-H-14-laion2B-s32B-b79K", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "clip_vision", "size": "2.53GB", "type": "clip_vision", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/image_encoder/model.safetensors"}, {"base": "ViT-G", "description": "CLIPVision model (needed for IP-Adapter)", "filename": "CLIP-ViT-bigG-14-laion2B-39B-b160k.safetensors", "name": "CLIPVision model (IP-Adapter) CLIP-ViT-bigG-14-laion2B-39B-b160k", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "clip_vision", "size": "3.69GB", "type": "clip_vision", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/image_encoder/model.safetensors"}, {"base": "SDXL", "description": "Control-LoRA: canny rank128", "filename": "control-lora-canny-rank128.safetensors", "name": "stabilityai/control-lora-canny-rank128.safetensors", "reference": "https://huggingface.co/stabilityai/control-lora", "save_path": "controlnet/SDXL", "size": "395.7MB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank128/control-lora-canny-rank128.safetensors"}, {"base": "SDXL", "description": "Control-LoRA: depth rank128", "filename": "control-lora-depth-rank128.safetensors", "name": "stabilityai/control-lora-depth-rank128.safetensors", "reference": "https://huggingface.co/stabilityai/control-lora", "save_path": "controlnet/SDXL", "size": "395.7MB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank128/control-lora-depth-rank128.safetensors"}, {"base": "SDXL", "description": "Control-LoRA: recolor rank128", "filename": "control-lora-recolor-rank128.safetensors", "name": "stabilityai/control-lora-recolor-rank128.safetensors", "reference": "https://huggingface.co/stabilityai/control-lora", "save_path": "controlnet/SDXL", "size": "395.7MB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank128/control-lora-recolor-rank128.safetensors"}, {"base": "SDXL", "description": "Control-LoRA: sketch rank128 metadata", "filename": "control-lora-sketch-rank128-metadata.safetensors", "name": "stabilityai/control-lora-sketch-rank128-metadata.safetensors", "reference": "https://huggingface.co/stabilityai/control-lora", "save_path": "controlnet/SDXL", "size": "395.7MB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank128/control-lora-sketch-rank128-metadata.safetensors"}, {"base": "SDXL", "description": "Control-LoRA: canny rank256", "filename": "control-lora-canny-rank256.safetensors", "name": "stabilityai/control-lora-canny-rank256.safetensors", "reference": "https://huggingface.co/stabilityai/control-lora", "save_path": "controlnet/SDXL", "size": "774.5MB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-canny-rank256.safetensors"}, {"base": "SDXL", "description": "Control-LoRA: depth rank256", "filename": "control-lora-depth-rank256.safetensors", "name": "stabilityai/control-lora-depth-rank256.safetensors", "reference": "https://huggingface.co/stabilityai/control-lora", "save_path": "controlnet/SDXL", "size": "774.4MB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-depth-rank256.safetensors"}, {"base": "SDXL", "description": "Control-LoRA: recolor rank256", "filename": "control-lora-recolor-rank256.safetensors", "name": "stabilityai/control-lora-recolor-rank256.safetensors", "reference": "https://huggingface.co/stabilityai/control-lora", "save_path": "controlnet/SDXL", "size": "774.4MB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-recolor-rank256.safetensors"}, {"base": "SDXL", "description": "Control-LoRA: sketch rank256", "filename": "control-lora-sketch-rank256.safetensors", "name": "stabilityai/control-lora-sketch-rank256.safetensors", "reference": "https://huggingface.co/stabilityai/control-lora", "save_path": "controlnet/SDXL", "size": "774.5MB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-sketch-rank256.safetensors"}, {"base": "SDXL", "description": "ControlNet openpose model for SDXL", "filename": "OpenPoseXL2.safetensors", "name": "SDXL-controlnet: OpenPose (v2)", "reference": "https://huggingface.co/thibaud/controlnet-openpose-sdxl-1.0", "save_path": "controlnet/SDXL", "size": "5.00GB", "type": "controlnet", "url": "https://huggingface.co/thibaud/controlnet-openpose-sdxl-1.0/resolve/main/OpenPoseXL2.safetensors"}, {"base": "SDXL", "description": "ControlNet softedge model for SDXL", "filename": "controlnet-sd-xl-1.0-softedge-dexined.safetensors", "name": "controlnet-SargeZT/controlnet-sd-xl-1.0-softedge-dexined", "reference": "https://huggingface.co/SargeZT/controlnet-sd-xl-1.0-softedge-dexined", "save_path": "controlnet/SDXL", "size": "5.00GB", "type": "controlnet", "url": "https://huggingface.co/SargeZT/controlnet-sd-xl-1.0-softedge-dexined/resolve/main/controlnet-sd-xl-1.0-softedge-dexined.safetensors"}, {"base": "SDXL", "description": "ControlNet depth-zoe model for SDXL", "filename": "depth-zoe-xl-v1.0-controlnet.safetensors", "name": "controlnet-SargeZT/controlnet-sd-xl-1.0-depth-16bit-zoe", "reference": "https://huggingface.co/SargeZT/controlnet-sd-xl-1.0-depth-16bit-zoe", "save_path": "controlnet/SDXL", "size": "5.00GB", "type": "controlnet", "url": "https://huggingface.co/SargeZT/controlnet-sd-xl-1.0-depth-16bit-zoe/resolve/main/depth-zoe-xl-v1.0-controlnet.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (ip2p)", "filename": "control_v11e_sd15_ip2p_fp16.safetensors", "name": "ControlNet-v1-1 (ip2p; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11e_sd15_ip2p_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (shuffle)", "filename": "control_v11e_sd15_shuffle_fp16.safetensors", "name": "ControlNet-v1-1 (shuffle; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11e_sd15_shuffle_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (canny)", "filename": "control_v11p_sd15_canny_fp16.safetensors", "name": "ControlNet-v1-1 (canny; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_canny_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (depth)", "filename": "control_v11f1p_sd15_depth_fp16.safetensors", "name": "ControlNet-v1-1 (depth; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11f1p_sd15_depth_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (inpaint)", "filename": "control_v11p_sd15_inpaint_fp16.safetensors", "name": "ControlNet-v1-1 (inpaint; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_inpaint_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (lineart)", "filename": "control_v11p_sd15_lineart_fp16.safetensors", "name": "ControlNet-v1-1 (lineart; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_lineart_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (mlsd)", "filename": "control_v11p_sd15_mlsd_fp16.safetensors", "name": "ControlNet-v1-1 (mlsd; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_mlsd_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (normalbae)", "filename": "control_v11p_sd15_normalbae_fp16.safetensors", "name": "ControlNet-v1-1 (normalbae; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_normalbae_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (openpose)", "filename": "control_v11p_sd15_openpose_fp16.safetensors", "name": "ControlNet-v1-1 (openpose; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_openpose_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (scribble)", "filename": "control_v11p_sd15_scribble_fp16.safetensors", "name": "ControlNet-v1-1 (scribble; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_scribble_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (seg)", "filename": "control_v11p_sd15_seg_fp16.safetensors", "name": "ControlNet-v1-1 (seg; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_seg_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (softedge)", "filename": "control_v11p_sd15_softedge_fp16.safetensors", "name": "ControlNet-v1-1 (softedge; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_softedge_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (anime)", "filename": "control_v11p_sd15s2_lineart_anime_fp16.safetensors", "name": "ControlNet-v1-1 (anime; fp16)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15s2_lineart_anime_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (tile) / v11u", "filename": "control_v11u_sd15_tile_fp16.safetensors", "name": "ControlNet-v1-1 (tile; fp16; v11u)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11u_sd15_tile_fp16.safetensors"}, {"base": "SD1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (tile) / v11f1e\nYou need to this model for <B>Tiled Resample</B>", "filename": "control_v11f1e_sd15_tile_fp16.safetensors", "name": "ControlNet-v1-1 (tile; fp16; v11f1e)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11f1e_sd15_tile_fp16.safetensors"}, {"base": "SD1.5", "description": "This inpaint-depth controlnet model is specialized for the hand refiner.", "filename": "control_sd15_inpaint_depth_hand_fp16.safetensors", "name": "ControlNet-HandRefiner-pruned (inpaint-depth-hand; fp16)", "reference": "https://huggingface.co/hr16/ControlNet-HandRefiner-pruned", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/hr16/ControlNet-HandRefiner-pruned/resolve/main/control_sd15_inpaint_depth_hand_fp16.safetensors"}, {"base": "SD1.5", "description": "Loose ControlNet model", "filename": "control_boxdepth_LooseControlfp16.safetensors", "name": "control_boxdepth_LooseControlfp16 (fp16)", "reference": "https://huggingface.co/ioclab/LooseControl_WebUICombine", "save_path": "controlnet/1.5", "size": "722.6MB", "type": "controlnet", "url": "https://huggingface.co/ioclab/LooseControl_WebUICombine/resolve/main/control_boxdepth_LooseControlfp16.safetensors"}, {"base": "SD1.5", "description": "GLIGEN textbox model", "filename": "gligen_sd14_textbox_pruned_fp16.safetensors", "name": "GLIGEN textbox (fp16; pruned)", "reference": "https://huggingface.co/comfyanonymous/GL<PERSON><PERSON>_pruned_safetensors", "save_path": "gligen/SD1.5", "size": "418.2MB", "type": "gligen", "url": "https://huggingface.co/comfyanonymous/G<PERSON><PERSON><PERSON>_pruned_safetensors/resolve/main/gligen_sd14_textbox_pruned_fp16.safetensors"}, {"base": "SAM", "description": "Segmenty Anything SAM model (ViT-H)", "filename": "sam_vit_h_4b8939.pth", "name": "ViT-H SAM model", "reference": "https://github.com/facebookresearch/segment-anything#model-checkpoints", "save_path": "sams", "size": "2.56GB", "type": "sam", "url": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"}, {"base": "SAM", "description": "Segmenty Anything SAM model (ViT-L)", "filename": "sam_vit_l_0b3195.pth", "name": "ViT-L SAM model", "reference": "https://github.com/facebookresearch/segment-anything#model-checkpoints", "save_path": "sams", "size": "1.25GB", "type": "sam", "url": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth"}, {"base": "SAM", "description": "Segmenty Anything SAM model (ViT-B)", "filename": "sam_vit_b_01ec64.pth", "name": "ViT-B SAM model", "reference": "https://github.com/facebookresearch/segment-anything#model-checkpoints", "save_path": "sams", "size": "375.0MB", "type": "sam", "url": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth"}, {"base": "SAM", "description": "Segmenty Anything SAM 2.1 hiera model (tiny)", "filename": "sam2.1_hiera_tiny.pt", "name": "sam2.1_hiera_tiny.pt", "reference": "https://github.com/facebookresearch/sam2#model-description", "save_path": "sams", "size": "149.0MB", "type": "sam2.1", "url": "https://dl.fbaipublicfiles.com/segment_anything_2/092824/sam2.1_hiera_tiny.pt"}, {"base": "SAM", "description": "Segmenty Anything SAM 2.1 hiera model (small)", "filename": "sam2.1_hiera_small.pt", "name": "sam2.1_hiera_small.pt", "reference": "https://github.com/facebookresearch/sam2#model-description", "save_path": "sams", "size": "176.0MB", "type": "sam2.1", "url": "https://dl.fbaipublicfiles.com/segment_anything_2/092824/sam2.1_hiera_small.pt"}, {"base": "SAM", "description": "Segmenty Anything SAM 2.1 hiera model (base+)", "filename": "sam2.1_hiera_base_plus.pt", "name": "sam2.1_hiera_base_plus.pt", "reference": "https://github.com/facebookresearch/sam2#model-description", "save_path": "sams", "size": "309.0MB", "type": "sam2.1", "url": "https://dl.fbaipublicfiles.com/segment_anything_2/092824/sam2.1_hiera_base_plus.pt"}, {"base": "SAM", "description": "Segmenty Anything SAM 2.1 hiera model (large)", "filename": "sam2.1_hiera_large.pt", "name": "sam2.1_hiera_large.pt", "reference": "https://github.com/facebookresearch/sam2#model-description", "save_path": "sams", "size": "857.0MB", "type": "sam2.1", "url": "https://dl.fbaipublicfiles.com/segment_anything_2/092824/sam2.1_hiera_large.pt"}, {"base": "SAM", "description": "Segmenty Anything SAM 2 hiera model (tiny)", "filename": "sam2_hiera_tiny.pt", "name": "sam2_hiera_tiny.pt", "reference": "https://github.com/facebookresearch/sam2#model-description", "save_path": "sams", "size": "149.0MB", "type": "sam2", "url": "https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_tiny.pt"}, {"base": "SAM", "description": "Segmenty Anything SAM 2 hiera model (small)", "filename": "sam2_hiera_small.pt", "name": "sam2_hiera_small.pt", "reference": "https://github.com/facebookresearch/sam2#model-description", "save_path": "sams", "size": "176.0MB", "type": "sam2", "url": "https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_small.pt"}, {"base": "SAM", "description": "Segmenty Anything SAM 2 hiera model (base+)", "filename": "sam2_hiera_base_plus.pt", "name": "sam2_hiera_base_plus.pt", "reference": "https://github.com/facebookresearch/sam2#model-description", "save_path": "sams", "size": "309.0MB", "type": "sam2", "url": "https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_base_plus.pt"}, {"base": "SAM", "description": "Segmenty Anything SAM 2 hiera model (large)", "filename": "sam2_hiera_large.pt", "name": "sam2_hiera_large.pt", "reference": "https://github.com/facebookresearch/sam2#model-description", "save_path": "sams", "size": "857.0MB", "type": "sam2", "url": "https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_large.pt"}, {"base": "SEECODER", "description": "SeeCoder model", "filename": "seecoder-v1-0.safetensors", "name": "seecoder v1.0", "reference": "https://huggingface.co/shi-labs/prompt-free-diffusion/tree/main/pretrained/pfd/seecoder", "save_path": "seecoders", "size": "1.18GB", "type": "seecoder", "url": "https://huggingface.co/shi-labs/prompt-free-diffusion/resolve/main/pretrained/pfd/seecoder/seecoder-v1-0.safetensors"}, {"base": "SEECODER", "description": "SeeCoder model", "filename": "seecoder-pa-v1-0.safetensors", "name": "seecoder pa v1.0", "reference": "https://huggingface.co/shi-labs/prompt-free-diffusion/tree/main/pretrained/pfd/seecoder", "save_path": "seecoders", "size": "1.19GB", "type": "seecoder", "url": "https://huggingface.co/shi-labs/prompt-free-diffusion/resolve/main/pretrained/pfd/seecoder/seecoder-pa-v1-0.safetensors"}, {"base": "SEECODER", "description": "SeeCoder model", "filename": "seecoder-anime-v1-0.safetensors", "name": "seecoder anime v1.0", "reference": "https://huggingface.co/shi-labs/prompt-free-diffusion/tree/main/pretrained/pfd/seecoder", "save_path": "seecoders", "size": "1.18GB", "type": "seecoder", "url": "https://huggingface.co/shi-labs/prompt-free-diffusion/resolve/main/pretrained/pfd/seecoder/seecoder-anime-v1-0.safetensors"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "face_yolov8m.pt", "name": "face_yolov8m (bbox)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/bbox", "size": "52.0MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/face_yolov8m.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "face_yolov8n.pt", "name": "face_yolov8n (bbox)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/bbox", "size": "6.23MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/face_yolov8n.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "face_yolov8n_v2.pt", "name": "face_yolov8n_v2 (bbox)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/bbox", "size": "6.24MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/face_yolov8n_v2.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "face_yolov8s.pt", "name": "face_yolov8s (bbox)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/bbox", "size": "22.5MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/face_yolov8s.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "hand_yolov8n.pt", "name": "hand_yolov8n (bbox)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/bbox", "size": "6.24MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/hand_yolov8n.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "hand_yolov8s.pt", "name": "hand_yolov8s (bbox)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/bbox", "size": "22.5MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/hand_yolov8s.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "person_yolov8m-seg.pt", "name": "person_yolov8m (segm)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/segm", "size": "54.8MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/person_yolov8m-seg.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "person_yolov8n-seg.pt", "name": "person_yolov8n (segm)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/segm", "size": "6.78MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/person_yolov8n-seg.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "person_yolov8s-seg.pt", "name": "person_yolov8s (segm)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/segm", "size": "23.9MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/person_yolov8s-seg.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "deepfashion2_yolov8s-seg.pt", "name": "deepfashion2_yolov8s (segm)", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "save_path": "ultralytics/segm", "size": "23.9MB", "type": "Ultralytics", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/deepfashion2_yolov8s-seg.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "face_yolov8m-seg_60.pt", "name": "face_yolov8m-seg_60.pt (segm)", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "save_path": "ultralytics/segm", "size": "54.8MB", "type": "Ultralytics", "url": "https://github.com/hben35096/assets/releases/download/yolo8/face_yolov8m-seg_60.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "face_yolov8n-seg2_60.pt", "name": "face_yolov8n-seg2_60.pt (segm)", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "save_path": "ultralytics/segm", "size": "6.77MB", "type": "Ultralytics", "url": "https://github.com/hben35096/assets/releases/download/yolo8/face_yolov8n-seg2_60.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "hair_yolov8n-seg_60.pt", "name": "hair_yolov8n-seg_60.pt (segm)", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "save_path": "ultralytics/segm", "size": "6.77MB", "type": "Ultralytics", "url": "https://github.com/hben35096/assets/releases/download/yolo8/hair_yolov8n-seg_60.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "skin_yolov8m-seg_400.pt", "name": "skin_yolov8m-seg_400.pt (segm)", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "save_path": "ultralytics/segm", "size": "54.9MB", "type": "Ultralytics", "url": "https://github.com/hben35096/assets/releases/download/yolo8/skin_yolov8m-seg_400.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "skin_yolov8n-seg_400.pt", "name": "skin_yolov8n-seg_400.pt (segm)", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "save_path": "ultralytics/segm", "size": "6.83MB", "type": "Ultralytics", "url": "https://github.com/hben35096/assets/releases/download/yolo8/skin_yolov8n-seg_400.pt"}, {"base": "Ultralytics", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "filename": "skin_yolov8n-seg_800.pt", "name": "skin_yolov8n-seg_800.pt (segm)", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "save_path": "ultralytics/segm", "size": "6.84MB", "type": "Ultralytics", "url": "https://github.com/hben35096/assets/releases/download/yolo8/skin_yolov8n-seg_800.pt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the ArtVentureX/AnimateDiff extension node.", "filename": "mm_sd_v14.ckpt", "name": "animatediff/mmd_sd_v14.ckpt (comfyui-animatediff) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "AnimateDiff", "size": "1.67GB", "type": "animatediff", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v14.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the ArtVentureX/AnimateDiff extension node.", "filename": "mm_sd_v15.ckpt", "name": "animatediff/mm_sd_v15.ckpt (comfyui-animatediff) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "AnimateDiff", "size": "1.67GB", "type": "animatediff", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v15.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "mm_sd_v14.ckpt", "name": "animatediff/mmd_sd_v14.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_models", "size": "1.67GB", "type": "animatediff", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v14.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "mm_sd_v15.ckpt", "name": "animatediff/mm_sd_v15.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_models", "size": "1.67GB", "type": "animatediff", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v15.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "mm_sd_v15_v2.ckpt", "name": "animatediff/mm_sd_v15_v2.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_models", "size": "1.82GB", "type": "animatediff", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v15_v2.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "v3_sd15_mm.ckpt", "name": "animatediff/v3_sd15_mm.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_models", "size": "1.67GB", "type": "animatediff", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v3_sd15_mm.ckpt"}, {"base": "SDXL", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "mm_sdxl_v10_beta.ckpt", "name": "animatediff/mm_sdxl_v10_beta.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_models", "size": "950.1MB", "type": "animatediff", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sdxl_v10_beta.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "mm-Stabilized_high.pth", "name": "AD_Stabilized_Motion/mm-Stabilized_high.pth (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/manshoety/AD_Stabilized_Motion", "save_path": "animatediff_models", "size": "1.67GB", "type": "animatediff", "url": "https://huggingface.co/manshoety/AD_Stabilized_Motion/resolve/main/mm-Stabilized_high.pth"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "mm-Stabilized_mid.pth", "name": "AD_Stabilized_Motion/mm-Stabilized_mid.pth (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/manshoety/AD_Stabilized_Motion", "save_path": "animatediff_models", "size": "1.67GB", "type": "animatediff", "url": "https://huggingface.co/manshoety/AD_Stabilized_Motion/resolve/main/mm-Stabilized_mid.pth"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "temporaldiff-v1-animatediff.ckpt", "name": "CiaraRowles/temporaldiff-v1-animatediff.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/CiaraRowles/TemporalDiff", "save_path": "animatediff_models", "size": "1.67GB", "type": "animatediff", "url": "https://huggingface.co/CiaraRowles/TemporalDiff/resolve/main/temporaldiff-v1-animatediff.ckpt"}, {"base": "SD1.x", "description": "AnimateDiff-PIA Model", "filename": "pia.ckpt", "name": "Leoxing/pia.ckpt", "reference": "https://huggingface.co/Leoxing/PIA/tree/main", "save_path": "animatediff_models", "size": "1.67GB", "type": "<PERSON>iff-pia", "url": "https://huggingface.co/Leoxing/PIA/resolve/main/pia.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "v2_lora_PanLeft.ckpt", "name": "animatediff/v2_lora_PanLeft.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_motion_lora", "size": "77.5MB", "type": "motion lora", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_PanLeft.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "v2_lora_PanRight.ckpt", "name": "animatediff/v2_lora_PanRight.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_motion_lora", "size": "77.5MB", "type": "motion lora", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_PanRight.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "v2_lora_RollingAnticlockwise.ckpt", "name": "animatediff/v2_lora_RollingAnticlockwise.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_motion_lora", "size": "77.5MB", "type": "motion lora", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_RollingAnticlockwise.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "v2_lora_RollingClockwise.ckpt", "name": "animatediff/v2_lora_RollingClockwise.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_motion_lora", "size": "77.5MB", "type": "motion lora", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_RollingClockwise.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "v2_lora_TiltDown.ckpt", "name": "animatediff/v2_lora_TiltDown.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_motion_lora", "size": "77.5MB", "type": "motion lora", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_TiltDown.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "v2_lora_TiltUp.ckpt", "name": "animatediff/v2_lora_TiltUp.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_motion_lora", "size": "77.5MB", "type": "motion lora", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_TiltUp.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "v2_lora_ZoomIn.ckpt", "name": "animatediff/v2_lora_ZoomIn.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_motion_lora", "size": "77.5MB", "type": "motion lora", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_ZoomIn.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "v2_lora_ZoomOut.ckpt", "name": "animatediff/v2_lora_ZoomOut.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "animatediff_motion_lora", "size": "77.5MB", "type": "motion lora", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_ZoomOut.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "lt_long_mm_32_frames.ckpt", "name": "LongAnimatediff/lt_long_mm_32_frames.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/Lightricks/LongAnimateDiff", "save_path": "animatediff_models", "size": "1.82GB", "type": "animatediff", "url": "https://huggingface.co/Lightricks/LongAnimateDiff/resolve/main/lt_long_mm_32_frames.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "lt_long_mm_16_64_frames.ckpt", "name": "LongAnimatediff/lt_long_mm_16_64_frames.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/Lightricks/LongAnimateDiff", "save_path": "animatediff_models", "size": "1.83GB", "type": "animatediff", "url": "https://huggingface.co/Lightricks/LongAnimateDiff/resolve/main/lt_long_mm_16_64_frames.ckpt"}, {"base": "SD1.x", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "filename": "lt_long_mm_16_64_frames_v1.1.ckpt", "name": "LongAnimatediff/lt_long_mm_16_64_frames_v1.1.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "reference": "https://huggingface.co/Lightricks/LongAnimateDiff", "save_path": "animatediff_models", "size": "1.83GB", "type": "animatediff", "url": "https://huggingface.co/Lightricks/LongAnimateDiff/resolve/main/lt_long_mm_16_64_frames_v1.1.ckpt"}, {"base": "SD1.x", "description": "AnimateDiff SparseCtrl RGB ControlNet model", "filename": "v3_sd15_sparsectrl_rgb.ckpt", "name": "animatediff/v3_sd15_sparsectrl_rgb.ckpt (ComfyUI-AnimateDiff-Evolved)", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "controlnet/SD1.5/animatediff", "size": "1.99GB", "type": "controlnet", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v3_sd15_sparsectrl_rgb.ckpt"}, {"base": "SD1.x", "description": "AnimateDiff SparseCtrl Scribble ControlNet model", "filename": "v3_sd15_sparsectrl_scribble.ckpt", "name": "animatediff/v3_sd15_sparsectrl_scribble.ckpt", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "controlnet/SD1.5/animatediff", "size": "1.99GB", "type": "controlnet", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v3_sd15_sparsectrl_scribble.ckpt"}, {"base": "SD1.x", "description": "AnimateDiff Adapter LoRA (SD1.5)", "filename": "v3_sd15_adapter.ckpt", "name": "animatediff/v3_sd15_adapter.ckpt", "reference": "https://huggingface.co/guoyww/animatediff", "save_path": "loras/SD1.5/animatediff", "size": "102.1MB", "type": "lora", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v3_sd15_adapter.ckpt"}, {"base": "MotionCtrl", "description": "To use the ComfyUI-MotionCtrl extension, downloading this model is required.", "filename": "motionctrl.pth", "name": "TencentARC/motionctrl.pth", "reference": "https://huggingface.co/TencentARC/MotionCtrl", "save_path": "checkpoints/motionctrl", "size": "4.02GB", "type": "checkpoint", "url": "https://huggingface.co/TencentARC/MotionCtrl/resolve/main/motionctrl.pth"}, {"base": "SD1.5", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip-adapter_sd15.safetensors", "name": "ip-adapter_sd15.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "44.6MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter_sd15.safetensors"}, {"base": "SD1.5", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip-adapter_sd15_light_v11.bin", "name": "ip-adapter_sd15_light_v11.bin", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "44.6MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter_sd15_light_v11.bin"}, {"base": "SD1.5", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip-adapter_sd15_light.safetensors", "name": "ip-adapter_sd15_light.safetensors [DEPRECATED]", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "44.6MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter_sd15_light.safetensors"}, {"base": "SD1.5", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip-adapter-plus_sd15.safetensors", "name": "ip-adapter-plus_sd15.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "98.2MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter-plus_sd15.safetensors"}, {"base": "SD1.5", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip-adapter-plus-face_sd15.safetensors", "name": "ip-adapter-plus-face_sd15.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "98.2MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter-plus-face_sd15.safetensors"}, {"base": "SD1.5", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip-adapter-full-face_sd15.safetensors", "name": "ip-adapter-full-face_sd15.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "43.6MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter-full-face_sd15.safetensors"}, {"base": "SD1.5", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip-adapter_sd15_vit-G.safetensors", "name": "ip-adapter_sd15_vit-G.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "46.2MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter_sd15_vit-G.safetensors"}, {"base": "SD1.5", "description": "IP-Adapter-FaceID Model (SD1.5) [ipadapter]", "filename": "ip-adapter-faceid_sd15.bin", "name": "ip-adapter-faceid_sd15.bin", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "ipadapter", "size": "96.7MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid_sd15.bin"}, {"base": "SD1.5", "description": "IP-Adapter-FaceID Plus V2 Model (SD1.5) [ipadapter]", "filename": "ip-adapter-faceid-plusv2_sd15.bin", "name": "ip-adapter-faceid-plusv2_sd15.bin", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "ipadapter", "size": "156.6MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plusv2_sd15.bin"}, {"base": "SD1.5", "description": "IP-Adapter-FaceID Plus Model (SD1.5) [ipadapter]", "filename": "ip-adapter-faceid-plus_sd15.bin", "name": "ip-adapter-faceid-plus_sd15.bin [DEPRECATED]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "ipadapter", "size": "156.6MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plus_sd15.bin"}, {"base": "SD1.5", "description": "IP-Adapter-FaceID Portrait V11 Model (SD1.5) [ipadapter]", "filename": "ip-adapter-faceid-portrait-v11_sd15.bin", "name": "ip-adapter-faceid-portrait-v11_sd15.bin", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "ipadapter", "size": "64.6MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-portrait-v11_sd15.bin"}, {"base": "SD1.5", "description": "IP-Adapter-FaceID Portrait Model (SD1.5) [ipadapter]", "filename": "ip-adapter-faceid-portrait_sd15.bin", "name": "ip-adapter-faceid-portrait_sd15.bin [DEPRECATED]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "ipadapter", "size": "64.6MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-portrait_sd15.bin"}, {"base": "SDXL", "description": "IP-Adapter-FaceID Model (SDXL) [ipadapter]", "filename": "ip-adapter-faceid_sdxl.bin", "name": "ip-adapter-faceid_sdxl.bin", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "ipadapter", "size": "1.07GB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid_sdxl.bin"}, {"base": "SDXL", "description": "IP-Adapter-FaceID Plus Model (SDXL) [ipadapter]", "filename": "ip-adapter-faceid-plusv2_sdxl.bin", "name": "ip-adapter-faceid-plusv2_sdxl.bin", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "ipadapter", "size": "1.49GB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plusv2_sdxl.bin"}, {"base": "SDXL", "description": "IP-Adapter-FaceID Portrait Model (SDXL) [ipadapter]", "filename": "ip-adapter-faceid-portrait_sdxl.bin", "name": "ip-adapter-faceid-portrait_sdxl.bin", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "ipadapter", "size": "749.8MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-portrait_sdxl.bin"}, {"base": "SDXL", "description": "IP-Adapter-FaceID Portrait Model (SDXL/unnorm) [ipadapter]", "filename": "ip-adapter-faceid-portrait_sdxl_unnorm.bin", "name": "ip-adapter-faceid-portrait_sdxl_unnorm.bin", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "ipadapter", "size": "1.01GB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-portrait_sdxl_unnorm.bin"}, {"base": "SD1.5", "description": "IP-Adapter-FaceID LoRA Model (SD1.5) [ipadapter]", "filename": "ip-adapter-faceid_sd15_lora.safetensors", "name": "ip-adapter-faceid_sd15_lora.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "loras/ipadapter", "size": "51.1MB", "type": "lora", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid_sd15_lora.safetensors"}, {"base": "SD1.5", "description": "IP-Adapter-FaceID Plus LoRA Model (SD1.5) [ipadapter]", "filename": "ip-adapter-faceid-plus_sd15_lora.safetensors", "name": "ip-adapter-faceid-plus_sd15_lora.safetensors [DEPRECATED]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "loras/ipadapter", "size": "51.1MB", "type": "lora", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plus_sd15_lora.safetensors"}, {"base": "SD1.5", "description": "IP-Adapter-FaceID-Plus V2 LoRA Model (SD1.5) [ipadapter]", "filename": "ip-adapter-faceid-plusv2_sd15_lora.safetensors", "name": "ip-adapter-faceid-plusv2_sd15_lora.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "loras/ipadapter", "size": "51.1MB", "type": "lora", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plusv2_sd15_lora.safetensors"}, {"base": "SDXL", "description": "IP-Adapter-FaceID LoRA Model (SDXL) [ipadapter]", "filename": "ip-adapter-faceid_sdxl_lora.safetensors", "name": "ip-adapter-faceid_sdxl_lora.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "loras/ipadapter", "size": "371.8MB", "type": "lora", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid_sdxl_lora.safetensors"}, {"base": "SDXL", "description": "IP-Adapter-FaceID-Plus V2 LoRA Model (SDXL) [ipadapter]", "filename": "ip-adapter-faceid-plusv2_sdxl_lora.safetensors", "name": "ip-adapter-faceid-plusv2_sdxl_lora.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "save_path": "loras/ipadapter", "size": "371.8MB", "type": "lora", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plusv2_sdxl_lora.safetensors"}, {"base": "SDXL", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip-adapter_sdxl.safetensors", "name": "ip-adapter_sdxl.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "702.6MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/ip-adapter_sdxl.safetensors"}, {"base": "SDXL", "description": "This model requires the use of the SD1.5 encoder despite being for SDXL checkpoints [ipadapter]", "filename": "ip-adapter_sdxl_vit-h.safetensors", "name": "ip-adapter_sdxl_vit-h.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "698.4MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/ip-adapter_sdxl_vit-h.safetensors"}, {"base": "SDXL", "description": "This model requires the use of the SD1.5 encoder despite being for SDXL checkpoints [ipadapter]", "filename": "ip-adapter-plus_sdxl_vit-h.safetensors", "name": "ip-adapter-plus_sdxl_vit-h.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "847.5MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/ip-adapter-plus_sdxl_vit-h.safetensors"}, {"base": "SDXL", "description": "This model requires the use of the SD1.5 encoder despite being for SDXL checkpoints [ipadapter]", "filename": "ip-adapter-plus-face_sdxl_vit-h.safetensors", "name": "ip-adapter-plus-face_sdxl_vit-h.safetensors", "reference": "https://huggingface.co/h94/IP-Adapter", "save_path": "ipadapter", "size": "847.5MB", "type": "IP-Adapter", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/ip-adapter-plus-face_sdxl_vit-h.safetensors"}, {"base": "SD1.5", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip_plus_composition_sd15.safetensors", "name": "ip_plus_composition_sd15.safetensors", "reference": "https://huggingface.co/ostris/ip-composition-adapter", "save_path": "ipadapter", "size": "98.2MB", "type": "IP-Adapter", "url": "https://huggingface.co/ostris/ip-composition-adapter/resolve/main/ip_plus_composition_sd15.safetensors"}, {"base": "SDXL", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "ip_plus_composition_sdxl.safetensors", "name": "ip_plus_composition_sdxl.safetensors", "reference": "https://huggingface.co/ostris/ip-composition-adapter", "save_path": "ipadapter", "size": "847.5MB", "type": "IP-Adapter", "url": "https://huggingface.co/ostris/ip-composition-adapter/resolve/main/ip_plus_composition_sdxl.safetensors"}, {"base": "<PERSON><PERSON><PERSON>", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "Kolors-IP-Adapter-Plus.bin", "name": "Kolors-IP-Adapter-Plus.bin (Kwai-Kolors/Kolors-IP-Adapter-Plus)", "reference": "https://huggingface.co/K<PERSON>-Kolors/Kolors-IP-Adapter-Plus", "save_path": "ipadapter", "size": "1.01GB", "type": "IP-Adapter", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors-IP-Adapter-Plus/resolve/main/ip_adapter_plus_general.bin"}, {"base": "<PERSON><PERSON><PERSON>", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "filename": "Kolors-IP-Adapter-FaceID-Plus.bin", "name": "Kolors-IP-Adapter-FaceID-Plus.bin (Kwai-Kolors/Kolors-IP-Adapter-Plus)", "reference": "https://huggingface.co/K<PERSON>-Kolors/Kolors-IP-Adapter-FaceID-Plus", "save_path": "ipadapter", "size": "2.39GB", "type": "IP-Adapter", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors-IP-Adapter-FaceID-Plus/resolve/main/ipa-faceid-plus.bin"}, {"base": "GFPGAN", "description": "Face Restoration Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "filename": "GFPGANv1.4.pth", "name": "GFPGANv1.4.pth", "reference": "https://github.com/TencentARC/GFPGAN/releases", "save_path": "facerestore_models", "size": "348.6MB", "type": "GFPGAN", "url": "https://github.com/TencentARC/GFPGAN/releases/download/v1.3.4/GFPGANv1.4.pth"}, {"base": "CodeFormer", "description": "Face Restoration Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "filename": "codeformer.pth", "name": "codeformer.pth", "reference": "https://github.com/sczhou/CodeFormer/releases", "save_path": "facerestore_models", "size": "376.6MB", "type": "CodeFormer", "url": "https://github.com/sczhou/CodeFormer/releases/download/v0.1.0/codeformer.pth"}, {"base": "facexlib", "description": "Face Detection Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "filename": "detection_Resnet50_Final.pth", "name": "detection_Resnet50_Final.pth", "reference": "https://github.com/xinntao/facexlib", "save_path": "facerestore_models", "size": "109.5MB", "type": "facexlib", "url": "https://github.com/xinntao/facexlib/releases/download/v0.1.0/detection_Resnet50_Final.pth"}, {"base": "facexlib", "description": "Face Detection Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "filename": "detection_mobilenet0.25_Final.pth", "name": "detection_mobilenet0.25_Final.pth", "reference": "https://github.com/xinntao/facexlib", "save_path": "facerestore_models", "size": "1.79MB", "type": "facexlib", "url": "https://github.com/xinntao/facexlib/releases/download/v0.1.0/detection_mobilenet0.25_Final.pth"}, {"base": "facexlib", "description": "Face Detection Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "filename": "yolov5l-face.pth", "name": "yolov5l-face.pth", "reference": "https://github.com/xinntao/facexlib", "save_path": "facedetection", "size": "187.0MB", "type": "facexlib", "url": "https://github.com/sczhou/CodeFormer/releases/download/v0.1.0/yolov5l-face.pth"}, {"base": "facexlib", "description": "Face Detection Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "filename": "yolov5n-face.pth", "name": "yolov5n-face.pth", "reference": "https://github.com/xinntao/facexlib", "save_path": "facedetection", "size": "7.15MB", "type": "facexlib", "url": "https://github.com/sczhou/CodeFormer/releases/download/v0.1.0/yolov5n-face.pth"}, {"base": "SDXL", "description": "PhotoMaker model. This model is compatible with SDXL.", "filename": "photomaker-v1.bin", "name": "photomaker-v1.bin", "reference": "https://huggingface.co/TencentARC/PhotoMaker", "save_path": "photomaker", "size": "934.1MB", "type": "photomaker", "url": "https://huggingface.co/TencentARC/PhotoMaker/resolve/main/photomaker-v1.bin"}, {"base": "SDXL", "description": "PhotoMaker model. This model is compatible with SDXL.", "filename": "photomaker-v2.bin", "name": "photomaker-v2.bin", "reference": "https://huggingface.co/TencentARC/PhotoMaker-V2", "save_path": "photomaker", "size": "1.8GB", "type": "photomaker", "url": "https://huggingface.co/TencentARC/PhotoMaker-V2/resolve/main/photomaker-v2.bin"}, {"base": "inswapper", "description": "Antelopev2 1k3d68.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "filename": "1k3d68.onnx", "name": "1k3d68.onnx", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "save_path": "insightface/models/antelopev2", "size": "143.6MB", "type": "insightface", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/1k3d68.onnx"}, {"base": "inswapper", "description": "Antelopev2 2d106det.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "filename": "2d106det.onnx", "name": "2d106det.onnx", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "save_path": "insightface/models/antelopev2", "size": "5.03MB", "type": "insightface", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/2d106det.onnx"}, {"base": "inswapper", "description": "Antelopev2 genderage.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "filename": "genderage.onnx", "name": "genderage.onnx", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "save_path": "insightface/models/antelopev2", "size": "1.32MB", "type": "insightface", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/genderage.onnx"}, {"base": "inswapper", "description": "Antelopev2 glintr100.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "filename": "glintr100.onnx", "name": "glintr100.onnx", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "save_path": "insightface/models/antelopev2", "size": "260.7MB", "type": "insightface", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/glintr100.onnx"}, {"base": "inswapper", "description": "Antelopev2 scrfd_10g_bnkps.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "filename": "scrfd_10g_bnkps.onnx", "name": "scrfd_10g_bnkps.onnx", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "save_path": "insightface/models/antelopev2", "size": "16.9MB", "type": "insightface", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/scrfd_10g_bnkps.onnx"}, {"base": "SDXL", "description": "InstantId main model based on IpAdapter", "filename": "ip-adapter.bin", "name": "ip-adapter.bin", "reference": "https://huggingface.co/InstantX/InstantID", "save_path": "instantid", "size": "1.69GB", "type": "instantid", "url": "https://huggingface.co/InstantX/InstantID/resolve/main/ip-adapter.bin"}, {"base": "SDXL", "description": "InstantId controlnet model", "filename": "diffusion_pytorch_model.safetensors", "name": "diffusion_pytorch_model.safetensors", "reference": "https://huggingface.co/InstantX/InstantID", "save_path": "controlnet/instantid", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/InstantX/InstantID/resolve/main/ControlNetModel/diffusion_pytorch_model.safetensors"}, {"base": "SD1.5", "description": "Fusers checkpoints for multi-object prompting with InstanceDiffusion.", "filename": "fusers.ckpt", "name": "InstanceDiffusion/fusers", "reference": "https://huggingface.co/logtd/instance_diffusion", "save_path": "instance_models/fuser_models", "size": "832.1MB", "type": "InstanceDiffusion", "url": "https://huggingface.co/logtd/instance_diffusion/resolve/main/fusers.ckpt"}, {"base": "SD1.5", "description": "PositionNet checkpoints for multi-object prompting with InstanceDiffusion.", "filename": "position_net.ckpt", "name": "InstanceDiffusion/position_net", "reference": "https://huggingface.co/logtd/instance_diffusion", "save_path": "instance_models/positionnet_models", "size": "643.2MB", "type": "InstanceDiffusion", "url": "https://huggingface.co/logtd/instance_diffusion/resolve/main/position_net.ckpt"}, {"base": "SD1.5", "description": "ScaleU checkpoints for multi-object prompting with InstanceDiffusion.", "filename": "scaleu.ckpt", "name": "InstanceDiffusion/scaleu", "reference": "https://huggingface.co/logtd/instance_diffusion", "save_path": "instance_models/scaleu_models", "size": "53.1KB", "type": "InstanceDiffusion", "url": "https://huggingface.co/logtd/instance_diffusion/resolve/main/scaleu.ckpt"}, {"base": "inswapper", "description": "Buffalo_l 1k3d68.onnx model for IpAdapterPlus", "filename": "1k3d68.onnx", "name": "1k3d68.onnx", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "save_path": "insightface/models/buffalo_l", "size": "143.6MB", "type": "insightface", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/1k3d68.onnx"}, {"base": "inswapper", "description": "Buffalo_l 2d106det.onnx model for IpAdapterPlus", "filename": "2d106det.onnx", "name": "2d106det.onnx", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "save_path": "insightface/models/buffalo_l", "size": "5.03MB", "type": "insightface", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/2d106det.onnx"}, {"base": "inswapper", "description": "Buffalo_l det_10g.onnx model for IpAdapterPlus", "filename": "det_10g.onnx", "name": "det_10g.onnx", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "save_path": "insightface/models/buffalo_l", "size": "16.9MB", "type": "insightface", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/det_10g.onnx"}, {"base": "inswapper", "description": "Buffalo_l genderage.onnx model for IpAdapterPlus", "filename": "genderage.onnx", "name": "genderage.onnx", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "save_path": "insightface/models/buffalo_l", "size": "1.32MB", "type": "insightface", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/genderage.onnx"}, {"base": "inswapper", "description": "Buffalo_l w600k_r50.onnx model for IpAdapterPlus", "filename": "w600k_r50.onnx", "name": "w600k_r50.onnx", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "save_path": "insightface/models/buffalo_l", "size": "174.4MB", "type": "insightface", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/w600k_r50.onnx"}, {"base": "blip_model", "description": "BLIP ImageCaption (COCO) w/ ViT-B and CapFilt-L", "filename": "model_base_capfilt_large.pth", "name": "BLIP ImageCaption (COCO) w/ ViT-B and CapFilt-L", "reference": "https://github.com/salesforce/BLIP", "save_path": "blip", "size": "2.12GB", "type": "BLIP_MODEL", "url": "https://storage.googleapis.com/sfr-vision-language-research/BLIP/models/model_base_capfilt_large.pth"}, {"base": "DINO", "description": "GroundingDINO SwinT OGC Model", "filename": "groundingdino_swint_ogc.pth", "name": "GroundingDINO SwinT OGC - Model", "reference": "https://huggingface.co/ShilongLiu/GroundingDINO", "save_path": "grounding<PERSON><PERSON>", "size": "694.0MB", "type": "GroundingDINO", "url": "https://huggingface.co/ShilongLiu/GroundingDINO/resolve/main/groundingdino_swint_ogc.pth"}, {"base": "DINO", "description": "GroundingDINO SwinT OGC CFG File", "filename": "GroundingDINO_SwinT_OGC.cfg.py", "name": "GroundingDINO SwinT OGC - CFG File", "reference": "https://huggingface.co/ShilongLiu/GroundingDINO/resolve/main/GroundingDINO_SwinT_OGC.cfg.py", "save_path": "grounding<PERSON><PERSON>", "size": "1.01KB", "type": "GroundingDINO", "url": "https://huggingface.co/ShilongLiu/GroundingDINO/raw/main/GroundingDINO_SwinT_OGC.cfg.py"}, {"base": "SAM", "description": "MobileSAM", "filename": "mobile_sam.pt", "name": "MobileSAM", "reference": "https://github.com/ChaoningZhang/MobileSAM/", "save_path": "sams", "size": "38.8MB", "type": "sam", "url": "https://github.com/ChaoningZhang/MobileSAM/blob/master/weights/mobile_sam.pt"}, {"base": "DynamiCrafter", "description": "DynamiCrafter image2video model 1024x575", "filename": "dynamicrafter_1024_v1_bf16.safetensors", "name": "DynamiCrafter 1024 bf16 safetensors", "reference": "https://huggingface.co/Kijai/DynamiCrafter_pruned/", "save_path": "checkpoints/dynamicrafter", "size": "5.22GB", "type": "checkpoint", "url": "https://huggingface.co/Kijai/DynamiCrafter_pruned/resolve/main/dynamicrafter_1024_v1_bf16.safetensors"}, {"base": "DynamiCrafter", "description": "DynamiCrafter image2video interpolation model 512", "filename": "dynamicrafter_512_interp_v1_bf16.safetensors", "name": "DynamiCrafter 512 interpolation bf16 safetensors", "reference": "https://huggingface.co/Kijai/DynamiCrafter_pruned/", "save_path": "checkpoints/dynamicrafter", "size": "5.22GB", "type": "checkpoint", "url": "https://huggingface.co/Kijai/DynamiCrafter_pruned/resolve/main/dynamicrafter_512_interp_v1_bf16.safetensors"}, {"base": "SDXL", "description": "monster-labs - Controlnet QR Code Monster v1 For SDXL", "filename": "control_v1p_sdxl_qrcode_monster.safetensors", "name": "monster-labs - Controlnet QR Code Monster v1 For SDXL", "reference": "https://huggingface.co/monster-labs/control_v1p_sdxl_qrcode_monster", "save_path": "controlnet/SDXL", "size": "5.00GB", "type": "controlnet", "url": "https://huggingface.co/monster-labs/control_v1p_sdxl_qrcode_monster/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "Depth-FM", "description": "Depth-FM monocular depth estimation model", "filename": "depthfm-v1_fp16.safetensors", "name": "Depth-FM-v1 fp16 safetensors", "reference": "https://huggingface.co/Kijai/depth-fm-pruned", "save_path": "checkpoints/depthfm", "size": "1.73GB", "type": "checkpoint", "url": "https://huggingface.co/Kijai/depth-fm-pruned/resolve/main/depthfm-v1_fp16.safetensors"}, {"base": "Depth-FM", "description": "Depth-FM monocular depth estimation model", "filename": "depthfm-v1_fp32.safetensors", "name": "Depth-FM-v1 fp32 safetensors", "reference": "https://huggingface.co/Kijai/depth-fm-pruned", "save_path": "checkpoints/depthfm", "size": "3.46GB", "type": "checkpoint", "url": "https://huggingface.co/Kijai/depth-fm-pruned/resolve/main/depthfm-v1_fp32.safetensors"}, {"base": "SUPIR", "description": "SUPIR checkpoint model", "filename": "SUPIR-v0F.ckpt", "name": "SUPIR-v0F.ckpt", "reference": "https://huggingface.co/camenduru/SUPIR/tree/main", "save_path": "checkpoints/SUPIR", "size": "5.33GB", "type": "checkpoint", "url": "https://huggingface.co/camenduru/SUPIR/resolve/main/SUPIR-v0F.ckpt"}, {"base": "SUPIR", "description": "SUPIR checkpoint model", "filename": "SUPIR-v0Q.ckpt", "name": "SUPIR-v0Q.ckpt", "reference": "https://huggingface.co/camenduru/SUPIR/tree/main", "save_path": "checkpoints/SUPIR", "size": "5.33GB", "type": "checkpoint", "url": "https://huggingface.co/camenduru/SUPIR/resolve/main/SUPIR-v0Q.ckpt"}, {"base": "SUPIR", "description": "SUPIR checkpoint model", "filename": "SUPIR-v0F_fp16.safetensors", "name": "Kijai/SUPIR-v0F_fp16.safetensors (pruned)", "reference": "https://huggingface.co/Kijai/SUPIR_pruned/tree/main", "save_path": "checkpoints/SUPIR", "size": "2.66GB", "type": "checkpoint", "url": "https://huggingface.co/Kijai/SUPIR_pruned/resolve/main/SUPIR-v0F_fp16.safetensors"}, {"base": "SUPIR", "description": "SUPIR checkpoint model", "filename": "SUPIR-v0Q_fp16.safetensors", "name": "Kijai/SUPIR-v0Q_fp16.safetensors (pruned)", "reference": "https://huggingface.co/Kijai/SUPIR_pruned/tree/main", "save_path": "checkpoints/SUPIR", "size": "2.66GB", "type": "checkpoint", "url": "https://huggingface.co/Kijai/SUPIR_pruned/resolve/main/SUPIR-v0Q_fp16.safetensors"}, {"base": "RAM", "description": "RAM Recognize Anything Model", "filename": "ram_swin_large_14m.pth", "name": "RAM", "reference": "https://huggingface.co/xinyu1205/recognize_anything_model", "save_path": "rams", "size": "5.63GB", "type": "RAM", "url": "https://huggingface.co/xinyu1205/recognize_anything_model/resolve/main/ram_swin_large_14m.pth"}, {"base": "RAM", "description": "RAM++ Recognize Anything Model", "filename": "ram_plus_swin_large_14m.pth", "name": "RAM++", "reference": "https://huggingface.co/xinyu1205/recognize-anything-plus-model", "save_path": "rams", "size": "3.01GB", "type": "RAM", "url": "https://huggingface.co/xinyu1205/recognize-anything-plus-model/resolve/main/ram_plus_swin_large_14m.pth"}, {"base": "RAM", "description": "tag2text Recognize Anything Model", "filename": "tag2text_swin_14m.pth", "name": "tag2text", "reference": "https://huggingface.co/xinyu1205/recognize_anything_model", "save_path": "rams", "size": "4.48GB", "type": "RAM", "url": "https://huggingface.co/xinyu1205/recognize_anything_model/resolve/main/tag2text_swin_14m.pth"}, {"base": "zero123", "description": "model that been trained on 10M+ 3D objects from Objaverse-XL, used for generated rotated CamView", "filename": "zero123-xl.ckpt", "name": "Zero123 3D object Model", "reference": "https://objaverse.allenai.org/docs/zero123-xl/", "save_path": "checkpoints/zero123", "size": "15.5GB", "type": "zero123", "url": "https://huggingface.co/kealiu/zero123-xl/resolve/main/zero123-xl.ckpt"}, {"base": "zero123", "description": "Stable Zero123 is a model for view-conditioned image generation based on [a/Zero123](https://github.com/cvlab-columbia/zero123).", "filename": "stable_zero123.ckpt", "name": "Zero123 3D object Model", "reference": "https://huggingface.co/stabilityai/stable-zero123", "save_path": "checkpoints/zero123", "size": "8.58GB", "type": "zero123", "url": "https://huggingface.co/stabilityai/stable-zero123/resolve/main/stable_zero123.ckpt"}, {"base": "zero123", "description": "Zero123 original checkpoints in 105000 steps.", "filename": "zero123-105000.ckpt", "name": "Zero123 3D object Model", "reference": "https://huggingface.co/cvlab/zero123-weights", "save_path": "checkpoints/zero123", "size": "15.5GB", "type": "zero123", "url": "https://huggingface.co/cvlab/zero123-weights/resolve/main/105000.ckpt"}, {"base": "zero123", "description": "Zero123 original checkpoints in 165000 steps.", "filename": "zero123-165000.ckpt", "name": "Zero123 3D object Model", "reference": "https://huggingface.co/cvlab/zero123-weights", "save_path": "checkpoints/zero123", "size": "15.5GB", "type": "zero123", "url": "https://huggingface.co/cvlab/zero123-weights/resolve/main/165000.ckpt"}, {"base": "SDXL", "description": "ip-adapter model for cubiq/InstantID", "filename": "ip-adapter.bin", "name": "InstantID/ip-adapter", "reference": "https://huggingface.co/InstantX/InstantID", "save_path": "instantid/SDXL", "size": "1.69GB", "type": "instantid", "url": "https://huggingface.co/InstantX/InstantID/resolve/main/ip-adapter.bin"}, {"base": "SDXL", "description": "instantid controlnet model for cubiq/InstantID", "filename": "diffusion_pytorch_model.safetensors", "name": "InstantID/ControlNet", "reference": "https://huggingface.co/InstantX/InstantID", "save_path": "controlnet/SDXL/instantid", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/InstantX/InstantID/resolve/main/ControlNetModel/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "MonsterMMORPG insightface model for cubiq/InstantID", "filename": "antelopev2.zip", "name": "MonsterMMORPG/insightface (for InstantID)", "reference": "https://huggingface.co/MonsterMMORPG/tools/tree/main", "save_path": "insightface/models", "size": "360.7MB", "type": "insightface", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/antelopev2.zip"}, {"base": "SD1.5", "description": "The default relighting model, conditioned on text and foreground", "filename": "iclight_sd15_fc.safetensors", "name": "IC-Light/fc", "reference": "https://huggingface.co/lllyasviel/ic-light", "save_path": "diffusion_models/IC-Light", "size": "1.72GB", "type": "IC-Light", "url": "https://huggingface.co/lllyasviel/ic-light/resolve/main/iclight_sd15_fc.safetensors"}, {"base": "SD1.5", "description": "Relighting model conditioned with text, foreground, and background", "filename": "iclight_sd15_fbc.safetensors", "name": "IC-Light/fbc", "reference": "https://huggingface.co/lllyasviel/ic-light", "save_path": "diffusion_models/IC-Light", "size": "1.72GB", "type": "IC-Light", "url": "https://huggingface.co/lllyasviel/ic-light/resolve/main/iclight_sd15_fbc.safetensors"}, {"base": "SD1.5", "description": "Same as iclight_sd15_fc.safetensors, but trained with offset noise", "filename": "iclight_sd15_fcon.safetensors", "name": "IC-Light/fcon", "reference": "https://huggingface.co/lllyasviel/ic-light", "save_path": "diffusion_models/IC-Light", "size": "1.72GB", "type": "IC-Light", "url": "https://huggingface.co/lllyasviel/ic-light/resolve/main/iclight_sd15_fcon.safetensors"}, {"base": "SDXL", "description": "Controlnet SDXL Tile model realistic version.", "filename": "TTPLANET_Controlnet_Tile_realistic_v2_fp16.safetensors", "name": "TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic v2 (fp16)", "reference": "https://huggingface.co/TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic", "save_path": "controlnet/SDXL", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic/resolve/main/TTPLANET_Controlnet_Tile_realistic_v2_fp16.safetensors"}, {"base": "SDXL", "description": "Controlnet SDXL Tile model realistic version.", "filename": "TTPLANET_Controlnet_Tile_realistic_v2_rank256.safetensors", "name": "TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic v2 (rank256)", "reference": "https://huggingface.co/TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic", "save_path": "controlnet/SDXL", "size": "774.4MB", "type": "controlnet", "url": "https://huggingface.co/TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic/resolve/main/TTPLANET_Controlnet_Tile_realistic_v2_rank256.safetensors"}, {"base": "RGT", "description": "RGT x2 upscale model for ComfyUI-RGT", "filename": "RGT_x2.pth", "name": "ViperYX/RGT_x2.pth", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "save_path": "RGT/RGT", "size": "179.8MB", "type": "RGT", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT/RGT_x2.pth"}, {"base": "RGT", "description": "RGT x3 upscale model for ComfyUI-RGT", "filename": "RGT_x3.pth", "name": "ViperYX/RGT_x3.pth", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "save_path": "RGT/RGT", "size": "180.5MB", "type": "RGT", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT/RGT_x3.pth"}, {"base": "RGT", "description": "RGT_S x4 upscale model for ComfyUI-RGT", "filename": "RGT_x4.pth", "name": "ViperYX/RGT_x4.pth", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "save_path": "RGT/RGT", "size": "180.4MB", "type": "RGT", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT/RGT_x4.pth"}, {"base": "RGT", "description": "RGT_S x2 upscale model for ComfyUI-RGT", "filename": "RGT_S_x2.pth", "name": "ViperYX/RGT_S_x2.pth", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "save_path": "RGT/RGT_S", "size": "135.4MB", "type": "RGT", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT_S/RGT_S_x2.pth"}, {"base": "RGT", "description": "RGT_S x3 upscale model for ComfyUI-RGT", "filename": "RGT_S_x3.pth", "name": "ViperYX/RGT_S_x3.pth", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "save_path": "RGT/RGT_S", "size": "136.1MB", "type": "RGT", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT_S/RGT_S_x3.pth"}, {"base": "RGT", "description": "RGT_S x4 upscale model for ComfyUI-RGT", "filename": "RGT_S_x4.pth", "name": "ViperYX/RGT_S_x4.pth", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "save_path": "RGT/RGT_S", "size": "136.0MB", "type": "RGT", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT_S/RGT_S_x4.pth"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Union Controlnet. Supports Canny, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Gray Low Quality.", "filename": "diffusion_pytorch_model.safetensors", "name": "InstantX/FLUX.1-dev Controlnet (Union)", "reference": "https://huggingface.co/InstantX/FLUX.1-dev-Controlnet-Union", "save_path": "controlnet/FLUX.1/InstantX-FLUX1-Dev-Union", "size": "6.6GB", "type": "controlnet", "url": "https://huggingface.co/InstantX/FLUX.1-dev-Controlnet-Union/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1-dev-IP-Adapter", "filename": "ip-adapter.bin", "name": "InstantX/FLUX.1-dev-IP-Adapter", "reference": "https://huggingface.co/InstantX/FLUX.1-dev-IP-Adapter", "save_path": "ipadapter-flux", "size": "5.29GB", "type": "IP-Adapter", "url": "https://huggingface.co/InstantX/FLUX.1-dev-IP-Adapter/resolve/main/ip-adapter.bin"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Union Controlnet. Supports <PERSON>ny, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Low Quality", "filename": "diffusion_pytorch_model.safetensors", "name": "Shakker-Labs/FLUX.1-dev-ControlNet-Union-Pro", "reference": "https://huggingface.co/Shakker-Labs/FLUX.1-dev-ControlNet-Union-Pro", "save_path": "controlnet/FLUX.1/Shakker-Labs-ControlNet-Union-Pro", "size": "6.6GB", "type": "controlnet", "url": "https://huggingface.co/Shakker-Labs/FLUX.1-dev-ControlNet-Union-Pro/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Union Controlnet. Supports <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Low Quality\nVersion quantized to fp8_e4m3fn by <PERSON><PERSON><PERSON>", "filename": "flux_shakker_labs_union_pro-fp8_e4m3fn.safetensors", "name": "Shakker-Labs/FLUX.1-dev-ControlNet-Union-Pro (fp8_e4m3fn) by <PERSON><PERSON><PERSON>", "reference": "https://huggingface.co/Kijai/flux-fp8", "save_path": "controlnet/FLUX.1", "size": "3.3GB", "type": "controlnet", "url": "https://huggingface.co/Kijai/flux-fp8/resolve/main/flux_shakker_labs_union_pro-fp8_e4m3fn.safetensors"}, {"base": "FLUX.1", "description": "This is Flux.1-dev ControlNet for low resolution images developed by Jasper research team.", "filename": "diffusion_pytorch_model.safetensors", "name": "jasperai/FLUX.1-dev-Controlnet-Upscaler", "reference": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Upscaler", "save_path": "controlnet/FLUX.1/jasperai-dev-Upscaler", "size": "3.58GB", "type": "controlnet", "url": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Upscaler/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "FLUX.1", "description": "This is Flux.1-dev ControlNet for Depth map developed by Jasper research team.", "filename": "diffusion_pytorch_model.safetensors", "name": "jasperai/FLUX.1-dev-Controlnet-Depth", "reference": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Depth", "save_path": "controlnet/FLUX.1/jasperai-dev-Depth", "size": "3.58GB", "type": "controlnet", "url": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Depth/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "FLUX.1", "description": "This is Flux.1-dev ControlNet for Surface Normals map developed by Jasper research team.", "filename": "diffusion_pytorch_model.safetensors", "name": "jasperai/Flux.1-dev-Controlnet-Surface-Normals", "reference": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Surface-Normals", "save_path": "controlnet/FLUX.1/jasperai-dev-Surface-Normals", "size": "3.58GB", "type": "controlnet", "url": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Surface-Normals/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "All-in-one ControlNet for image generations and editing!", "filename": "diffusion_pytorch_model.safetensors", "name": "xinsir/ControlNet++: All-in-one ControlNet", "reference": "https://huggingface.co/xinsir/controlnet-union-sdxl-1.0", "save_path": "controlnet/SDXL/controlnet-union-sdxl-1.0", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/xinsir/controlnet-union-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "All-in-one ControlNet for image generations and editing! (ProMax model)", "filename": "diffusion_pytorch_model_promax.safetensors", "name": "xinsir/ControlNet++: All-in-one ControlNet (ProMax model)", "reference": "https://huggingface.co/xinsir/controlnet-union-sdxl-1.0", "save_path": "controlnet/SDXL/controlnet-union-sdxl-1.0", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/xinsir/controlnet-union-sdxl-1.0/resolve/main/diffusion_pytorch_model_promax.safetensors"}, {"base": "SDXL", "description": "Controlnet SDXL Scribble model.", "filename": "diffusion_pytorch_model.safetensors", "name": "xinsir/Controlnet-Scribble-Sdxl-1.0", "reference": "https://huggingface.co/xinsir/controlnet-scribble-sdxl-1.0", "save_path": "controlnet/SDXL/controlnet-scribble-sdxl-1.0", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/xinsir/controlnet-scribble-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "Controlnet SDXL Canny model.", "filename": "diffusion_pytorch_model_V2.safetensors", "name": "xinsir/Controlnet-Canny-Sdxl-1.0 (V2)", "reference": "https://huggingface.co/xinsir/controlnet-canny-sdxl-1.0", "save_path": "controlnet/SDXL/controlnet-canny-sdxl-1.0", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/xinsir/controlnet-canny-sdxl-1.0/resolve/main/diffusion_pytorch_model_V2.safetensors"}, {"base": "SDXL", "description": "Controlnet SDXL Openpose model.", "filename": "diffusion_pytorch_model.safetensors", "name": "xinsir/Controlnet-Openpose-Sdxl-1.0", "reference": "https://huggingface.co/xinsir/controlnet-openpose-sdxl-1.0", "save_path": "controlnet/SDXL/controlnet-openpose-sdxl-1.0", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/xinsir/controlnet-openpose-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "Controlnet SDXL Openpose model. (Ver. twins)", "filename": "diffusion_pytorch_model_twins.safetensors", "name": "xinsir/Controlnet-Openpose-Sdxl-1.0 (Ver. twins)", "reference": "https://huggingface.co/xinsir/controlnet-openpose-sdxl-1.0", "save_path": "controlnet/SDXL/controlnet-openpose-sdxl-1.0", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/xinsir/controlnet-openpose-sdxl-1.0/resolve/main/diffusion_pytorch_model_twins.safetensors"}, {"base": "SDXL", "description": "Controlnet SDXL Scribble model. (Ver. anime)", "filename": "diffusion_pytorch_model.safetensors", "name": "xinsir/Controlnet-Scribble-Sdxl-1.0-Anime", "reference": "https://huggingface.co/xinsir/anime-painter", "save_path": "controlnet/SDXL/controlnet-scribble-sdxl-1.0-anime", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/xinsir/anime-painter/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "Controlnet SDXL Depth model.", "filename": "diffusion_pytorch_model.safetensors", "name": "xinsir/ControlNet Depth SDXL, support zoe, midias", "reference": "https://huggingface.co/xinsir/controlnet-depth-sdxl-1.0", "save_path": "controlnet/SDXL/controlnet-depth-sdxl-1.0", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/xinsir/controlnet-depth-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SDXL", "description": "Controlnet SDXL Tile model.", "filename": "diffusion_pytorch_model.safetensors", "name": "xinsir/ControlNet Tile SDXL", "reference": "https://huggingface.co/xinsir/controlnet-tile-sdxl-1.0", "save_path": "controlnet/SDXL/controlnet-tile-sdxl-1.0", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/xinsir/controlnet-tile-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SD3", "description": "Controlnet SD3 Canny model.", "filename": "diffusion_pytorch_model.safetensors", "name": "InstantX/SD3-Controlnet-Canny", "reference": "https://huggingface.co/InstantX/SD3-Controlnet-Canny", "save_path": "controlnet/SD3/InstantX-Controlnet-Canny", "size": "1.19GB", "type": "controlnet", "url": "https://huggingface.co/InstantX/SD3-Controlnet-Canny/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SD3", "description": "Controlnet SD3 Pose model.", "filename": "diffusion_pytorch_model.safetensors", "name": "InstantX/SD3-Controlnet-Pose", "reference": "https://huggingface.co/InstantX/SD3-Controlnet-Pose", "save_path": "controlnet/SD3/InstantX-Controlnet-Pose", "size": "1.19GB", "type": "controlnet", "url": "https://huggingface.co/InstantX/SD3-Controlnet-Pose/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SD3", "description": "Controlnet SD3 Tile model.", "filename": "diffusion_pytorch_model.safetensors", "name": "InstantX/SD3-Controlnet-Tile", "reference": "https://huggingface.co/InstantX/SD3-Controlnet-Tile", "save_path": "controlnet/SD3/InstantX-Controlnet-Tile", "size": "1.19GB", "type": "controlnet", "url": "https://huggingface.co/InstantX/SD3-Controlnet-Tile/resolve/main/diffusion_pytorch_model.safetensors"}, {"base": "SD3.5", "description": "Blur Controlnet model for SD3.5 Large", "filename": "sd3.5_large_controlnet_blur.safetensors", "name": "stabilityai/SD3.5-Large-Controlnet-Blur", "reference": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets", "save_path": "controlnet/SD3.5", "size": "8.65GB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets/resolve/main/sd3.5_large_controlnet_blur.safetensors"}, {"base": "SD3.5", "description": "Canny Controlnet model for SD3.5 Large", "filename": "sd3.5_large_controlnet_canny.safetensors", "name": "stabilityai/SD3.5-Large-Controlnet-Canny", "reference": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets", "save_path": "controlnet/SD3.5", "size": "8.65GB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets/resolve/main/sd3.5_large_controlnet_canny.safetensors"}, {"base": "SD3.5", "description": "Depth Controlnet model for SD3.5 Large", "filename": "sd3.5_large_controlnet_depth.safetensors", "name": "stabilityai/SD3.5-Large-Controlnet-Depth", "reference": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets", "save_path": "controlnet/SD3.5", "size": "8.65GB", "type": "controlnet", "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets/resolve/main/sd3.5_large_controlnet_depth.safetensors"}, {"base": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "ToonCrafter checkpoint model for ComfyUI-DynamiCrafterWrapper", "filename": "tooncrafter_512_interp-fp16.safetensors", "name": "Kijai/ToonCrafter model checkpoint (interpolation fp16)", "reference": "https://huggingface.co/Kijai/DynamiCrafter_pruned", "save_path": "checkpoints/ToonCrafter", "size": "5.25GB", "type": "checkpoint", "url": "https://huggingface.co/Kijai/DynamiCrafter_pruned/resolve/main/tooncrafter_512_interp-fp16.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet. A model for style transfer.", "filename": "CN-anytest_v4-marged.safetensors", "name": "CN-anytest_v4-marged.safetensors", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet Lora (dim256) for Animagine. A model for style transfer.", "filename": "CN-anytest_v4-marged_am_dim256.safetensors", "name": "CN-anytest_v4-marged_am_dim256.safetensors (dim256/Animagine)", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "774.4MB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged_am_dim256.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet Lora (dim128) for Animagine. A model for style transfer.", "filename": "CN-anytest_v4-marged_am_dim128.safetensors", "name": "CN-anytest_v4-marged_am_dim128.safetensors (dim128/Animagine)", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "395.7MB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged_am_dim128.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet Lora (dim256) for Pony. A model for style transfer.", "filename": "CN-anytest_v4-marged_pn_dim256.safetensors", "name": "CN-anytest_v4-marged_pn_dim256.safetensors (dim256/Pony)", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "774.4MB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged_pn_dim256.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet Lora (dim128) for Pony. A model for style transfer.", "filename": "CN-anytest_v4-marged_pn_dim128.safetensors", "name": "CN-anytest_v4-marged_pn_dim128.safetensors (dim128/Pony)", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "395.7MB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged_pn_dim128.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet. A strict control model.", "filename": "CN-anytest_v3-50000_fp16.safetensors", "name": "CN-anytest_v3-50000_fp16.safetensors (fp16)", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "2.50GB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_fp16.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet Lora (dim256) for Animagine. A strict control model.", "filename": "CN-anytest_v3-50000_am_dim256.safetensors", "name": "CN-anytest_v3-50000_am_dim256.safetensors (dim256/Animagine)", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "774.4MB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_am_dim256.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet Lora (dim128) for Animagine. A strict control model.", "filename": "CN-anytest_v3-50000_am_dim128.safetensors", "name": "CN-anytest_v3-50000_am_dim128.safetensors (dim128/Animagine)", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "395.7MB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_am_dim128.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet Lora (dim256) for Pony. A strict control model.", "filename": "CN-anytest_v3-50000_pn_dim256.safetensors", "name": "CN-anytest_v3-50000_pn_dim256.safetensors (dim256/Pony)", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "774.4MB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_pn_dim256.safetensors"}, {"base": "SDXL", "description": "AnyTest Controlnet Lora (dim128) for Pony. A strict control model.", "filename": "CN-anytest_v3-50000_pn_dim128.safetensors", "name": "CN-anytest_v3-50000_pn_dim128.safetensors (dim128/Pony)", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "save_path": "controlnet/SDXL", "size": "395.7MB", "type": "controlnet", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_pn_dim128.safetensors"}, {"base": "depthanything", "description": "DepthAnythingV2 model", "filename": "depth_anything_v2_vitb_fp16.safetensors", "name": "kijai/DepthAnythingV2 (vitb/fp16)", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "save_path": "depthanything", "size": "195.0MB", "type": "depthanything", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vitb_fp16.safetensors"}, {"base": "depthanything", "description": "DepthAnythingV2 model", "filename": "depth_anything_v2_vitb_fp32.safetensors", "name": "kijai/DepthAnythingV2 (vitb/fp32)", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "save_path": "depthanything", "size": "389.9MB", "type": "depthanything", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vitb_fp32.safetensors"}, {"base": "depthanything", "description": "DepthAnythingV2 model", "filename": "depth_anything_v2_vitl_fp16.safetensors", "name": "kijai/DepthAnythingV2 (vitl/fp16)", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "save_path": "depthanything", "size": "670.7MB", "type": "depthanything", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vitl_fp16.safetensors"}, {"base": "depthanything", "description": "DepthAnythingV2 model", "filename": "depth_anything_v2_vitl_fp32.safetensors", "name": "kijai/DepthAnythingV2 (vitl/fp32)", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "save_path": "depthanything", "size": "1.34GB", "type": "depthanything", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vitl_fp32.safetensors"}, {"base": "depthanything", "description": "DepthAnythingV2 model", "filename": "depth_anything_v2_vits_fp16.safetensors", "name": "kijai/DepthAnythingV2 (vits/fp16)", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "save_path": "depthanything", "size": "49.6MB", "type": "depthanything", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vits_fp16.safetensors"}, {"base": "depthanything", "description": "DepthAnythingV2 model", "filename": "depth_anything_v2_vits_fp32.safetensors", "name": "kijai/DepthAnythingV2 (vitb/fp32)", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "save_path": "depthanything", "size": "99.2MB", "type": "depthanything", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vits_fp32.safetensors"}, {"base": "pixart-sigma", "description": "PixArt-Sigma Checkpoint model", "filename": "PixArt-Sigma-XL-2-1024-MS.pth", "name": "PixArt-Sigma-XL-2-1024-MS.pth (checkpoint)", "reference": "https://huggingface.co/PixArt-alpha/PixArt-Sigma/tree/main", "save_path": "checkpoints/PixArt-Sigma", "size": "2.47GB", "type": "checkpoint", "url": "https://huggingface.co/PixArt-alpha/PixArt-Sigma/resolve/main/PixArt-Sigma-XL-2-1024-MS.pth"}, {"base": "pixart-sigma", "description": "PixArt-Sigma Diffusion model", "filename": "PixArt-Sigma-XL-2-512-MS.safetensors", "name": "PixArt-Sigma-XL-2-512-MS.safetensors (diffusion)", "reference": "https://huggingface.co/PixArt-alpha/PixArt-Sigma-XL-2-512-MS", "save_path": "diffusion_models/PixArt-Sigma", "size": "2.44GB", "type": "diffusion_model", "url": "https://huggingface.co/PixArt-alpha/PixArt-Sigma-XL-2-512-MS/resolve/main/transformer/diffusion_pytorch_model.safetensors"}, {"base": "pixart-sigma", "description": "PixArt-Sigma Diffusion model", "filename": "PixArt-Sigma-XL-2-1024-MS.safetensors", "name": "PixArt-Sigma-XL-2-1024-MS.safetensors (diffusion)", "reference": "https://huggingface.co/PixArt-alpha/PixArt-Sigma-XL-2-1024-MS", "save_path": "diffusion_models/PixArt-Sigma", "size": "2.44GB", "type": "diffusion_model", "url": "https://huggingface.co/PixArt-alpha/PixArt-Sigma-XL-2-1024-MS/resolve/main/transformer/diffusion_pytorch_model.safetensors"}, {"base": "pixart-alpha", "description": "PixArt-Alpha Diffusion model", "filename": "PixArt-XL-2-1024-MS.safetensors", "name": "PixArt-XL-2-1024-MS.safetensors (diffusion)", "reference": "https://huggingface.co/PixArt-alpha/PixArt-XL-2-1024-MS", "save_path": "diffusion_models/PixArt-Alpha", "size": "2.45GB", "type": "diffusion_model", "url": "https://huggingface.co/PixArt-alpha/PixArt-XL-2-1024-MS/resolve/main/transformer/diffusion_pytorch_model.safetensors"}, {"base": "Hunyuan-DiT", "description": "Different versions of HunyuanDIT packaged for ComfyUI use.", "filename": "hunyuan_dit_1.2.safetensors", "name": "hunyuan_dit_1.2.safetensors", "reference": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui", "save_path": "checkpoints/hunyuan_dit_comfyui", "size": "8.24GB", "type": "checkpoint", "url": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui/resolve/main/hunyuan_dit_1.2.safetensors"}, {"base": "Hunyuan-DiT", "description": "Different versions of HunyuanDIT packaged for ComfyUI use.", "filename": "hunyuan_dit_1.1.safetensors", "name": "hunyuan_dit_1.1.safetensors", "reference": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui", "save_path": "checkpoints/hunyuan_dit_comfyui", "size": "8.24GB", "type": "checkpoint", "url": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui/resolve/main/hunyuan_dit_1.1.safetensors"}, {"base": "Hunyuan-DiT", "description": "Different versions of HunyuanDIT packaged for ComfyUI use.", "filename": "hunyuan_dit_1.0.safetensors", "name": "hunyuan_dit_1.0.safetensors", "reference": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui", "save_path": "checkpoints/hunyuan_dit_comfyui", "size": "8.24GB", "type": "checkpoint", "url": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui/resolve/main/hunyuan_dit_1.0.safetensors"}, {"base": "Hunyuan Video", "description": "Huyuan Video diffusion model. repackaged version.", "filename": "hun<PERSON>_video_t2v_720p_bf16.safetensors", "name": "Comfy-Org/hunyuan_video_t2v_720p_bf16.safetensors", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "save_path": "diffusion_models/hunyuan_video", "size": "25.6GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/diffusion_models/hunyuan_video_t2v_720p_bf16.safetensors"}, {"base": "Hunyuan Video", "description": "Huyuan Video VAE model. repackaged version.", "filename": "hunyuan_video_vae_bf16.safetensors", "name": "Comfy-Org/hunyuan_video_vae_bf16.safetensors", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "save_path": "default", "size": "493MB", "type": "VAE", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/vae/hunyuan_video_vae_bf16.safetensors"}, {"base": "Hunyuan Video", "description": "Huyuan Video Image2Video diffusion model. repackaged version.", "filename": "hunyuan_video_image_to_video_720p_bf16.safetensors", "name": "Comfy-Org/hunyuan_video_image_to_video_720p_bf16.safetensors", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "save_path": "diffusion_models/hunyuan_video", "size": "25.6GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/diffusion_models/hunyuan_video_image_to_video_720p_bf16.safetensors"}, {"base": "LLaVA-Llama-3", "description": "llava_llama3_fp8_scaled text encoder model. This is required for using Hunyuan Video.", "filename": "llava_llama3_fp8_scaled.safetensors", "name": "Comfy-Org/llava_llama3_fp8_scaled.safetensors", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "save_path": "text_encoders", "size": "9.09GB", "type": "clip", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/text_encoders/llava_llama3_fp8_scaled.safetensors"}, {"base": "LLaVA-Llama-3", "description": "llava_llama3_fp16 text encoder model. This is required for using Hunyuan Video.", "filename": "llava_llama3_fp16.safetensors", "name": "Comfy-Org/llava_llama3_fp16.safetensors", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "save_path": "text_encoders", "size": "16.1GB", "type": "clip", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/text_encoders/llava_llama3_fp16.safetensors"}, {"base": "LLaVA-Llama-3", "description": "llava_llama3_vision clip vison model. This is required for using Hunyuan Video Image2Video.", "filename": "llava_llama3_vision.safetensors", "name": "Comfy-Org/llava_llama3_vision.safetensors", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "save_path": "text_encoders", "size": "649MB", "type": "clip_vision", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/clip_vision/llava_llama3_vision.safetensors"}, {"base": "OmniGen2", "description": "OmniGen2 diffusion model. This is required for using OmniGen2.", "filename": "omnigen2_fp16.safetensors", "name": "Comfy-Org/omnigen2_fp16.safetensors", "reference": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged", "save_path": "default", "size": "7.93GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/omnigen2_fp16.safetensors"}, {"base": "qwen-2.5", "description": "text encoder for OmniGen2", "filename": "qwen_2.5_vl_fp16.safetensors", "name": "Comfy-Org/qwen_2.5_vl_fp16.safetensors", "reference": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged", "save_path": "default", "size": "7.51GB", "type": "clip", "url": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged/resolve/main/split_files/text_encoders/qwen_2.5_vl_fp16.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1 [<PERSON><PERSON><PERSON>] Diffusion model (a.k.a. FLUX.1 turbo model)[w/Due to the large size of the model, it is recommended to download it through a browser if possible.]", "filename": "flux1-schnell.safetensors", "name": "FLUX.1 [<PERSON><PERSON><PERSON>] Diffusion model", "reference": "https://huggingface.co/black-forest-labs/FLUX.1-schnell", "save_path": "diffusion_models/FLUX1", "size": "23.8GB", "type": "diffusion_model", "url": "https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/flux1-schnell.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1 VAE model\nNOTE: This VAE model can also be used for image generation with OmniGen2.", "filename": "ae.safetensors", "name": "FLUX.1 VAE model", "reference": "https://huggingface.co/black-forest-labs/FLUX.1-schnell", "save_path": "vae/FLUX1", "size": "335MB", "type": "VAE", "url": "https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1 [<PERSON><PERSON><PERSON>] Diffusion model (float8_e4m3fn)", "filename": "flux1-schnell-fp8.safetensors", "name": "kijai/FLUX.1 [schnell] Diffusion model (float8_e4m3fn)", "reference": "https://huggingface.co/Kijai/flux-fp8", "save_path": "diffusion_models/FLUX1", "size": "11.9GB", "type": "diffusion_model", "url": "https://huggingface.co/Kijai/flux-fp8/resolve/main/flux1-schnell-fp8.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (scaled fp8)[w/Due to the large size of the model, it is recommended to download it through a browser if possible.]", "filename": "flux_dev_fp8_scaled_diffusion_model.safetensors", "name": "FLUX.1 [Dev] Diffusion model (scaled fp8)", "reference": "https://huggingface.co/comfyanonymous/flux_dev_scaled_fp8_test", "save_path": "diffusion_models/FLUX1", "size": "11.9GB", "type": "diffusion_model", "url": "https://huggingface.co/comfyanonymous/flux_dev_scaled_fp8_test/resolve/main/flux_dev_fp8_scaled_diffusion_model.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1 [dev] Diffusion model (float8_e4m3fn)", "filename": "flux1-dev-fp8.safetensors", "name": "kijai/FLUX.1 [dev] Diffusion model (float8_e4m3fn)", "reference": "https://huggingface.co/Kijai/flux-fp8", "save_path": "diffusion_models/FLUX1", "size": "11.9GB", "type": "diffusion_model", "url": "https://huggingface.co/Kijai/flux-fp8/resolve/main/flux1-dev-fp8.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1 [dev] Checkpoint model (fp8)", "filename": "flux1-dev-fp8.safetensors", "name": "Comfy Org/FLUX.1 [dev] Checkpoint model (fp8)", "reference": "https://huggingface.co/Comfy-Org/flux1-dev/tree/main", "save_path": "checkpoints/FLUX1", "size": "17.2GB", "type": "checkpoint", "url": "https://huggingface.co/Comfy-Org/flux1-dev/resolve/main/flux1-dev-fp8.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1 [schnell] Checkpoint model (fp8)", "filename": "flux1-schnell-fp8.safetensors", "name": "Comfy Org/FLUX.1 [schnell] Checkpoint model (fp8)", "reference": "https://huggingface.co/Comfy-Org/flux1-dev/tree/main", "save_path": "checkpoints/FLUX1", "size": "17.2GB", "type": "checkpoint", "url": "https://huggingface.co/Comfy-Org/flux1-schnell/resolve/main/flux1-schnell-fp8.safetensors"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (f16/.gguf)", "filename": "flux1-dev-F16.gguf", "name": "city96/flux1-dev-F16.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "23.8GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-F16.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q2_K/.gguf)", "filename": "flux1-dev-Q2_K.gguf", "name": "city96/flux1-dev-Q2_K.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "4.03GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q2_K.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q3_K_S/.gguf)", "filename": "flux1-dev-Q3_K_S.gguf", "name": "city96/flux1-dev-Q3_K_S.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "5.23GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q3_K_S.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q4_0/.gguf)", "filename": "flux1-dev-Q4_0.gguf", "name": "city96/flux1-dev-Q4_0.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "6.79GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q4_0.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q4_1/.gguf)", "filename": "flux1-dev-Q4_1.gguf", "name": "city96/flux1-dev-Q4_1.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "7.53GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q4_1.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q4_K_S/.gguf)", "filename": "flux1-dev-Q4_K_S.gguf", "name": "city96/flux1-dev-Q4_K_S.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "6.81GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q4_K_S.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q5_0/.gguf)", "filename": "flux1-dev-Q5_0.gguf", "name": "city96/flux1-dev-Q5_0.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "8.27GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q5_0.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q5_1/.gguf)", "filename": "flux1-dev-Q5_1.gguf", "name": "city96/flux1-dev-Q5_1.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "9.01GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q5_1.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q5_K_S/.gguf)", "filename": "flux1-dev-Q5_K_S.gguf", "name": "city96/flux1-dev-Q5_K_S.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "8.29GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q5_K_S.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q6_K/.gguf)", "filename": "flux1-dev-Q6_K.gguf", "name": "city96/flux1-dev-Q6_K.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "9.86GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q6_K.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q8_0/.gguf)", "filename": "flux1-dev-Q8_0.gguf", "name": "city96/flux1-dev-Q8_0.gguf", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "save_path": "diffusion_models/FLUX1", "size": "12.7GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q8_0.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (f16/.gguf)", "filename": "flux1-s<PERSON><PERSON>-F16.gguf", "name": "city96/flux1-schnell-F16.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "23.8GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-F16.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q2_K/.gguf)", "filename": "flux1-schnell-Q2_<PERSON>.gguf", "name": "city96/flux1-schnell-Q2_K.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "4.01GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q2_K.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q3_K_S/.gguf)", "filename": "flux1-schnell-Q3_K_S.gguf", "name": "city96/flux1-schnell-Q3_K_S.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "5.21GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q3_K_S.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q4_0/.gguf)", "filename": "flux1-schnell-Q4_0.gguf", "name": "city96/flux1-schnell-Q4_0.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "6.77GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q4_0.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q4_1/.gguf)", "filename": "flux1-schnell-Q4_1.gguf", "name": "city96/flux1-schnell-Q4_1.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "7.51GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q4_1.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q4_K_S/.gguf)", "filename": "flux1-schnell-Q4_K_S.gguf", "name": "city96/flux1-sch<PERSON>-Q4_K_S.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "6.78GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q4_K_S.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q5_0/.gguf)", "filename": "flux1-schnell-Q5_0.gguf", "name": "city96/flux1-schnell-Q5_0.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "8.25GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q5_0.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q5_1/.gguf)", "filename": "flux1-schnell-Q5_1.gguf", "name": "city96/flux1-schnell-Q5_1.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "8.99GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q5_1.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q5_K_S/.gguf)", "filename": "flux1-sch<PERSON>-Q5_K_S.gguf", "name": "city96/flux1-sch<PERSON>-Q5_K_S.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "8.26GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q5_K_S.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q6_K/.gguf)", "filename": "flux1-sch<PERSON>-Q6_<PERSON>.gguf", "name": "city96/flux1-schnell-Q6_K.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "9.83GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q6_K.gguf"}, {"base": "FLUX.1", "description": "FLUX.1 [Dev] Diffusion model (Q8_0/.gguf)", "filename": "flux1-schnell-Q8_0.gguf", "name": "city96/flux1-schnell-Q8_0.gguf", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "save_path": "diffusion_models/FLUX1", "size": "12.7GB", "type": "diffusion_model", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q8_0.gguf"}, {"base": "clip", "description": "Greatly improved TEXT + Detail (as CLIP-L for Flux.1)", "filename": "ViT-L-14-TEXT-detail-improved-hiT-GmP-HF.safetensors", "name": "ViT-L-14-TEXT-detail-improved-hiT-GmP-HF.safetensors [Long CLIP L]", "reference": "https://huggingface.co/zer0int", "save_path": "text_encoders/long_clip", "size": "931MB", "type": "clip", "url": "https://huggingface.co/zer0int/CLIP-GmP-ViT-L-14/resolve/main/ViT-L-14-TEXT-detail-improved-hiT-GmP-HF.safetensors"}, {"base": "clip", "description": "Greatly improved TEXT + Detail (as CLIP-L for Flux.1)", "filename": "ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors", "name": "ViT-L-14-TEXT-detail-improved-hiT-GmP-HF.safetensors [Long CLIP L]", "reference": "https://huggingface.co/zer0int", "save_path": "text_encoders/long_clip", "size": "323MB", "type": "clip", "url": "https://huggingface.co/zer0int/CLIP-GmP-ViT-L-14/resolve/main/ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors"}, {"base": "depth-pro", "description": "Depth pro model for [a/ComfyUI-Depth-Pro](https://github.com/spacepxl/ComfyUI-Depth-Pro)", "filename": "depth_pro.fp16.safetensors", "name": "Depth Pro model", "reference": "https://huggingface.co/spacepxl/ml-depth-pro", "save_path": "depth/ml-depth-pro", "size": "1.9GB", "type": "depth-pro", "url": "https://huggingface.co/spacepxl/ml-depth-pro/resolve/main/depth_pro.fp16.safetensors"}, {"base": "lotus", "description": "lotus depth d model v1.1 (fp16). This model can be used in ComfyUI-Lotus custom nodes.", "filename": "lotus-depth-d-v-1-1-fp16.safetensors", "name": "kijai/lotus depth d model v1.1 (fp16)", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "save_path": "diffusion_models", "size": "1.74GB", "type": "diffusion_model", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-depth-d-v-1-1-fp16.safetensors"}, {"base": "lotus", "description": "lotus depth g model v1.0 (fp16). This model can be used in ComfyUI-Lotus custom nodes.", "filename": "lotus-depth-g-v1-0-fp16.safetensors", "name": "kijai/lotus depth g model v1.0 (fp16)", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "save_path": "diffusion_models", "size": "1.74GB", "type": "diffusion_model", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-depth-g-v1-0-fp16.safetensors"}, {"base": "lotus", "description": "lotus depth g model v1.0. This model can be used in ComfyUI-Lotus custom nodes.", "filename": "lotus-depth-g-v1-0.safetensors", "name": "kijai/lotus depth g model v1.0", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "save_path": "diffusion_models", "size": "3.47GB", "type": "diffusion_model", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-depth-g-v1-0.safetensors"}, {"base": "lotus", "description": "lotus normal d model v1.0 (fp16). This model can be used in ComfyUI-Lotus custom nodes.", "filename": "lotus-normal-d-v1-0-fp16.safetensors", "name": "kijai/lotus normal d model v1.0 (fp16)", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "save_path": "diffusion_models", "size": "1.74GB", "type": "diffusion_model", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-normal-d-v1-0-fp16.safetensors"}, {"base": "lotus", "description": "lotus normal d model v1.0. This model can be used in ComfyUI-Lotus custom nodes.", "filename": "lotus-normal-d-v1-0.safetensors", "name": "kijai/lotus normal d model v1.0", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "save_path": "diffusion_models", "size": "3.47GB", "type": "diffusion_model", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-normal-d-v1-0.safetensors"}, {"base": "lotus", "description": "lotus normal g model v1.0 (fp16). This model can be used in ComfyUI-Lotus custom nodes.", "filename": "lotus-normal-g-v1-0-fp16.safetensors", "name": "kijai/lotus normal g model v1.0 (fp16)", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "save_path": "diffusion_models", "size": "1.74GB", "type": "diffusion_model", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-normal-g-v1-0-fp16.safetensors"}, {"base": "lotus", "description": "lotus normal g model v1.0. This model can be used in ComfyUI-Lotus custom nodes.", "filename": "lotus-normal-g-v1-0.safetensors", "name": "kijai/lotus normal g model v1.0", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "save_path": "diffusion_models", "size": "3.47GB", "type": "diffusion_model", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-normal-g-v1-0.safetensors"}, {"base": "<PERSON><PERSON><PERSON>", "description": "Kolors UNet model", "filename": "diffusion_pytorch_model.safetensors", "name": "Kolors UNet model", "reference": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors", "save_path": "diffusion_models/kolors", "size": "10.3GB", "type": "diffusion_model", "url": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors/resolve/main/unet/diffusion_pytorch_model.safetensors"}, {"base": "<PERSON><PERSON><PERSON>", "description": "Kolors UNet model", "filename": "diffusion_pytorch_model.fp16.safetensors", "name": "Kolors UNet model (fp16)", "reference": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors", "save_path": "diffusion_models/kolors", "size": "5.16GB", "type": "diffusion_model", "url": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors/resolve/main/unet/diffusion_pytorch_model.fp16.safetensors"}, {"base": "ChatGLM3", "description": "This is required for <PERSON><PERSON><PERSON>", "filename": "chatglm3-4bit.safetensors", "name": "Kijai/ChatGLM3 (4bit)", "reference": "https://huggingface.co/Kijai/ChatGLM3-safetensors/tree/main", "save_path": "LLM", "size": "3.92GB", "type": "LLM", "url": "https://huggingface.co/Kijai/ChatGLM3-safetensors/resolve/main/chatglm3-4bit.safetensors"}, {"base": "ChatGLM3", "description": "This is required for <PERSON><PERSON><PERSON>", "filename": "chatglm3-8bit.safetensors", "name": "Kijai/ChatGLM3 (8bit)", "reference": "https://huggingface.co/Kijai/ChatGLM3-safetensors/tree/main", "save_path": "LLM", "size": "3.92GB", "type": "LLM", "url": "https://huggingface.co/Kijai/ChatGLM3-safetensors/resolve/main/chatglm3-8bit.safetensors"}, {"base": "ChatGLM3", "description": "This is required for <PERSON><PERSON><PERSON>", "filename": "chatglm3-fp16.safetensors", "name": "Kijai/ChatGLM3 (16bit)", "reference": "https://huggingface.co/Kijai/ChatGLM3-safetensors/tree/main", "save_path": "LLM", "size": "12.52GB", "type": "LLM", "url": "https://huggingface.co/Kijai/ChatGLM3-safetensors/resolve/main/chatglm3-fp16.safetensors"}, {"base": "FLUX.1", "description": "This is required for PuLID (FLUX)", "filename": "pulid_flux_v0.9.1.safetensors", "name": "pulid_flux_v0.9.1.safetensors", "reference": "https://huggingface.co/guozinan/PuLID", "save_path": "pu<PERSON>", "size": "1.14GB", "type": "PuLID", "url": "https://huggingface.co/guozinan/PuLID/resolve/main/pulid_flux_v0.9.1.safetensors"}, {"base": "SDXL", "description": "This is required for PuLID (SDXL)", "filename": "pulid_v1.1.safetensors", "name": "pulid_v1.1.safetensors", "reference": "https://huggingface.co/guozinan/PuLID", "save_path": "pu<PERSON>", "size": "984MB", "type": "PuLID", "url": "https://huggingface.co/guozinan/PuLID/resolve/main/pulid_v1.1.safetensors"}, {"base": "MoGe", "description": "Safetensors versions of [a/https://github.com/microsoft/MoGe](https://github.com/microsoft/MoGe)", "filename": "MoGe_ViT_L_fp16.safetensors", "name": "kijai/MoGe_ViT_L_fp16.safetensors", "reference": "https://huggingface.co/Kijai/MoGe_safetensors", "save_path": "MoGe", "size": "628MB", "type": "MoGe", "url": "https://huggingface.co/Kijai/MoGe_safetensors/resolve/main/MoGe_ViT_L_fp16.safetensors"}, {"base": "MoGe", "description": "Safetensors versions of [a/https://github.com/microsoft/MoGe](https://github.com/microsoft/MoGe)", "filename": "MoGe_ViT_L_fp16.safetensors", "name": "kijai/MoGe_ViT_L_fp16.safetensors", "reference": "https://huggingface.co/Kijai/MoGe_safetensors", "save_path": "MoGe", "size": "1.26GB", "type": "MoGe", "url": "https://huggingface.co/Kijai/MoGe_safetensors/resolve/main/MoGe_ViT_L_fp16.safetensors"}, {"base": "LTX-Video", "description": "LTX-Video is the first DiT-based video generation model capable of generating high-quality videos in real-time. It produces 24 FPS videos at a 768x512 resolution faster than they can be watched. Trained on a large-scale dataset of diverse videos, the model generates high-resolution videos with realistic and varied content.", "filename": "ltx-video-2b-v0.9.safetensors", "name": "LTX-Video 2B v0.9 Checkpoint", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "9.37GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltx-video-2b-v0.9.safetensors"}, {"base": "LTX-Video", "description": "LTX-Video is the first DiT-based video generation model capable of generating high-quality videos in real-time. It produces 24 FPS videos at a 768x512 resolution faster than they can be watched. Trained on a large-scale dataset of diverse videos, the model generates high-resolution videos with realistic and varied content.", "filename": "ltx-video-2b-v0.9.1.safetensors", "name": "LTX-Video 2B v0.9.1 Checkpoint", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "5.72GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltx-video-2b-v0.9.1.safetensors"}, {"base": "LTX-Video", "description": "LTX-Video is the first DiT-based video generation model capable of generating high-quality videos in real-time. It produces 24 FPS videos at a 768x512 resolution faster than they can be watched. Trained on a large-scale dataset of diverse videos, the model generates high-resolution videos with realistic and varied content.", "filename": "ltx-video-2b-v0.9.5.safetensors", "name": "LTX-Video 2B v0.9.5 Checkpoint", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "6.34GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltx-video-2b-v0.9.5.safetensors"}, {"base": "FLUX.1", "description": "ControlNet checkpoints for FLUX.1-dev model by Black Forest Labs.", "filename": "flux-canny-controlnet-v3.safetensors", "name": "XLabs-AI/flux-canny-controlnet-v3.safetensors", "reference": "https://huggingface.co/XLabs-AI/flux-controlnet-collections", "save_path": "xlabs/controlnets", "size": "1.49GB", "type": "controlnet", "url": "https://huggingface.co/XLabs-AI/flux-controlnet-collections/resolve/main/flux-canny-controlnet-v3.safetensors"}, {"base": "FLUX.1", "description": "ControlNet checkpoints for FLUX.1-dev model by Black Forest Labs.", "filename": "flux-depth-controlnet-v3.safetensors", "name": "XLabs-AI/flux-depth-controlnet-v3.safetensors", "reference": "https://huggingface.co/XLabs-AI/flux-controlnet-collections", "save_path": "xlabs/controlnets", "size": "1.49GB", "type": "controlnet", "url": "https://huggingface.co/XLabs-AI/flux-controlnet-collections/resolve/main/flux-depth-controlnet-v3.safetensors"}, {"base": "FLUX.1", "description": "ControlNet checkpoints for FLUX.1-dev model by Black Forest Labs.", "filename": "flux-hed-controlnet-v3.safetensors", "name": "XLabs-AI/flux-hed-controlnet-v3.safetensors", "reference": "https://huggingface.co/XLabs-AI/flux-controlnet-collections", "save_path": "xlabs/controlnets", "size": "1.49GB", "type": "controlnet", "url": "https://huggingface.co/XLabs-AI/flux-controlnet-collections/resolve/main/flux-hed-controlnet-v3.safetensors"}, {"base": "FLUX.1", "description": "A checkpoint with trained LoRAs for FLUX.1-dev model by Black Forest Labs", "filename": "realism_lora.safetensors", "name": "XLabs-AI/realism_lora.safetensors", "reference": "https://huggingface.co/XLabs-AI/flux-lora-collection", "save_path": "xlabs/loras", "size": "44.8MB", "type": "lora", "url": "https://huggingface.co/XLabs-AI/flux-lora-collection/resolve/main/realism_lora.safetensors"}, {"base": "FLUX.1", "description": "A checkpoint with trained LoRAs for FLUX.1-dev model by Black Forest Labs", "filename": "art_lora.safetensors", "name": "XLabs-AI/art_lora.safetensors", "reference": "https://huggingface.co/XLabs-AI/flux-lora-collection", "save_path": "xlabs/loras", "size": "44.8MB", "type": "lora", "url": "https://huggingface.co/XLabs-AI/flux-lora-collection/resolve/main/scenery_lora.safetensors"}, {"base": "FLUX.1", "description": "A checkpoint with trained LoRAs for FLUX.1-dev model by Black Forest Labs", "filename": "mjv6_lora.safetensors", "name": "XLabs-AI/mjv6_lora.safetensors", "reference": "https://huggingface.co/XLabs-AI/flux-lora-collection", "save_path": "xlabs/loras", "size": "44.8MB", "type": "lora", "url": "https://huggingface.co/XLabs-AI/flux-lora-collection/resolve/main/mjv6_lora.safetensors"}, {"base": "FLUX.1", "description": "A checkpoint with trained LoRAs for FLUX.1-dev model by Black Forest Labs", "filename": "ip_adapter.safetensors", "name": "XLabs-AI/flux-ip-adapter", "reference": "https://huggingface.co/XLabs-AI/flux-ip-adapter", "save_path": "xlabs/ipadapters", "size": "982MB", "type": "lora", "url": "https://huggingface.co/XLabs-AI/flux-ip-adapter/resolve/main/ip_adapter.safetensors"}, {"base": "efficient_sam", "description": "Install efficient_sam_s_cpu.jit into ComfyUI-YoloWorld-EfficientSAM", "filename": "efficient_sam_s_cpu.jit", "name": "efficient_sam_s_cpu.jit [ComfyUI-YoloWorld-EfficientSAM]", "reference": "https://huggingface.co/camenduru/YoloWorld-EfficientSAM/tree/main", "save_path": "yolo_world", "size": "106.0MB", "type": "efficient_sam", "url": "https://huggingface.co/camenduru/YoloWorld-EfficientSAM/resolve/main/efficient_sam_s_cpu.jit"}, {"base": "efficient_sam", "description": "Install efficient_sam_s_gpu.jit into ComfyUI-YoloWorld-EfficientSAM", "filename": "efficient_sam_s_gpu.jit", "name": "efficient_sam_s_gpu.jit [ComfyUI-YoloWorld-EfficientSAM]", "reference": "https://huggingface.co/camenduru/YoloWorld-EfficientSAM/tree/main", "save_path": "yolo_world", "size": "106.0MB", "type": "efficient_sam", "url": "https://huggingface.co/camenduru/YoloWorld-EfficientSAM/resolve/main/efficient_sam_s_gpu.jit"}, {"base": "CustomNet", "description": "CustomNet pretrained model for ComfyUI_CustomNet", "filename": "customnet_v1.pt", "name": "TencentARC/CustomNet V1", "reference": "https://huggingface.co/TencentARC/CustomNet/tree/main", "save_path": "checkpoints/customnet", "size": "5.71GB", "type": "CustomNet", "url": "https://huggingface.co/TencentARC/CustomNet/resolve/main/customnet_v1.pt"}, {"base": "CustomNet", "description": "CustomNet Inpaint pretrained model for ComfyUI_CustomNet", "filename": "customnet_inpaint_v1.pt", "name": "TencentARC/CustomNet Inpaint V1", "reference": "https://huggingface.co/TencentARC/CustomNet/tree/main", "save_path": "checkpoints/customnet", "size": "5.71GB", "type": "CustomNet", "url": "https://huggingface.co/TencentARC/CustomNet/resolve/main/customnet_inpaint_v1.pt"}, {"base": "<PERSON><PERSON><PERSON><PERSON>", "description": "[SNAPSHOT] Janus-Pro-1B model.[w/You cannot download this item on ComfyUI-Manager versions below V3.18]", "filename": "<huggingface>", "name": "deepseek-ai/Janus-Pro-1B", "reference": "https://huggingface.co/deepseek-ai/<PERSON><PERSON>-Pro-1B", "save_path": "<PERSON><PERSON><PERSON><PERSON>", "size": "7.8GB", "type": "<PERSON><PERSON><PERSON><PERSON>", "url": "deepseek-ai/Janus-Pro-1B"}, {"base": "<PERSON><PERSON><PERSON><PERSON>", "description": "[SNAPSHOT] Janus-Pro-7B model.[w/You cannot download this item on ComfyUI-Manager versions below V3.18]", "filename": "<huggingface>", "name": "deepseek-ai/Janus-Pro-7B", "reference": "https://huggingface.co/deepseek-ai/<PERSON><PERSON>-<PERSON>-7B", "save_path": "<PERSON><PERSON><PERSON><PERSON>", "size": "14.85GB", "type": "<PERSON><PERSON><PERSON><PERSON>", "url": "deepseek-ai/Janus-Pro-7B"}, {"base": "<PERSON><PERSON><PERSON>", "description": "Kolors VAE", "filename": "diffusion_pytorch_model.fp16.safetensors", "name": "kolors/vae/diffusion_pytorch_model.fp16.safetensors", "reference": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors", "save_path": "vae/kolors", "size": "167MB", "type": "VAE", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors/resolve/main/vae/diffusion_pytorch_model.fp16.safetensors"}, {"base": "<PERSON><PERSON><PERSON>", "description": "Kolors VAE", "filename": "diffusion_pytorch_model.safetensors", "name": "kolors/vae/diffusion_pytorch_model.safetensors", "reference": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors", "save_path": "vae/kolors", "size": "335MB", "type": "VAE", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors/resolve/main/vae/diffusion_pytorch_model.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for i2v 480p 14B (bf16)", "filename": "wan2.1_i2v_480p_14B_bf16.safetensors", "name": "Comfy-Org/Wan2.1 i2v 480p 14B (bf16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "32.8GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_480p_14B_bf16.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for i2v 480p 14B (fp16)", "filename": "wan2.1_i2v_480p_14B_fp16.safetensors", "name": "Comfy-Org/Wan2.1 i2v 480p 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "32.8GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_480p_14B_fp16.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for i2v 480p 14B (fp8_e4m3fn)", "filename": "wan2.1_i2v_480p_14B_fp8_e4m3fn.safetensors", "name": "Comfy-Org/Wan2.1 i2v 480p 14B (fp8_e4m3fn)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "16.4GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_480p_14B_fp8_e4m3fn.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for i2v 480p 14B (fp8_scaled)", "filename": "wan2.1_i2v_480p_14B_fp8_scaled.safetensors", "name": "Comfy-Org/Wan2.1 i2v 480p 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "16.4GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_480p_14B_fp8_scaled.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for i2v 720p 14B (bf16)", "filename": "wan2.1_i2v_720p_14B_bf16.safetensors", "name": "Comfy-Org/Wan2.1 i2v 720p 14B (bf16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "32.8GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_720p_14B_bf16.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for i2v 720p 14B (fp16)", "filename": "wan2.1_i2v_720p_14B_fp16.safetensors", "name": "Comfy-Org/Wan2.1 i2v 720p 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "32.8GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_720p_14B_fp16.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for i2v 720p 14B (fp8_e4m3fn)", "filename": "wan2.1_i2v_720p_14B_fp8_e4m3fn.safetensors", "name": "Comfy-Org/Wan2.1 i2v 720p 14B (fp8_e4m3fn)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "16.4GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_720p_14B_fp8_e4m3fn.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for i2v 720p 14B (fp8_scaled)", "filename": "wan2.1_i2v_720p_14B_fp8_scaled.safetensors", "name": "Comfy-Org/Wan2.1 i2v 720p 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "16.4GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_720p_14B_fp8_scaled.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for t2v 1.3B (bf16)", "filename": "wan2.1_t2v_1.3B_bf16.safetensors", "name": "Comfy-Org/Wan2.1 t2v 1.3B (bf16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "2.84GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_bf16.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for t2v 1.3B (fp16)", "filename": "wan2.1_t2v_1.3B_fp16.safetensors", "name": "Comfy-Org/Wan2.1 t2v 1.3B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "2.84GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_fp16.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for t2v 14B (bf16)", "filename": "wan2.1_t2v_14B_bf16.safetensors", "name": "Comfy-Org/Wan2.1 t2v 14B (bf16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "28.6GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_14B_bf16.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for t2v 14B (fp16)", "filename": "wan2.1_t2v_14B_fp16.safetensors", "name": "Comfy-Org/Wan2.1 t2v 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "28.6GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_14B_fp16.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for t2v 14B (fp8_e4m3fn)", "filename": "wan2.1_t2v_14B_fp8_e4m3fn.safetensors", "name": "Comfy-Org/Wan2.1 t2v 14B (fp8_e4m3fn)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "14.3GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_14B_fp8_e4m3fn.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 difussion model for t2v 14B (fp8_scaled)", "filename": "wan2.1_t2v_14B_fp8_scaled.safetensors", "name": "Comfy-Org/Wan2.1 t2v 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "diffusion_models/Wan2.1", "size": "14.3GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_14B_fp8_scaled.safetensors"}, {"base": "Wan2.1", "description": "Wan2.1 VAE model", "filename": "wan_2.1_vae.safetensors", "name": "Comfy-Org/Wan2.1 VAE", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "vae", "size": "254MB", "type": "vae", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors"}, {"base": "clip_vision_h", "description": "clip_vision_h model for Wan2.1", "filename": "clip_vision_h.safetensors", "name": "Comfy-Org/clip_vision_h.safetensors", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "clip_vision", "size": "1.26GB", "type": "clip_vision", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors"}, {"base": "Wan2.2", "description": "Wan2.2 diffusion model for i2v high noise 14B (fp16)", "filename": "wan2.2_i2v_high_noise_14B_fp16.safetensors", "name": "Comfy-Org/Wan2.2 i2v high noise 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged", "save_path": "diffusion_models/Wan2.2", "size": "28.6GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged/resolve/main/split_files/diffusion_models/wan2.2_i2v_high_noise_14B_fp16.safetensors"}, {"base": "Wan2.2", "description": "Wan2.2 diffusion model for i2v high noise 14B (fp8_scaled)", "filename": "wan2.2_i2v_high_noise_14B_fp8_scaled.safetensors", "name": "Comfy-Org/Wan2.2 i2v high noise 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged", "save_path": "diffusion_models/Wan2.2", "size": "14.3GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged/resolve/main/split_files/diffusion_models/wan2.2_i2v_high_noise_14B_fp8_scaled.safetensors"}, {"base": "Wan2.2", "description": "Wan2.2 diffusion model for i2v low noise 14B (fp16)", "filename": "wan2.2_i2v_low_noise_14B_fp16.safetensors", "name": "Comfy-Org/Wan2.2 i2v low noise 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged", "save_path": "diffusion_models/Wan2.2", "size": "28.6GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged/resolve/main/split_files/diffusion_models/wan2.2_i2v_low_noise_14B_fp16.safetensors"}, {"base": "Wan2.2", "description": "Wan2.2 diffusion model for i2v low noise 14B (fp8_scaled)", "filename": "wan2.2_i2v_low_noise_14B_fp8_scaled.safetensors", "name": "Comfy-Org/Wan2.2 i2v low noise 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged", "save_path": "diffusion_models/Wan2.2", "size": "14.3GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged/resolve/main/split_files/diffusion_models/wan2.2_i2v_low_noise_14B_fp8_scaled.safetensors"}, {"base": "Wan2.2", "description": "Wan2.2 diffusion model for t2v high noise 14B (fp16)", "filename": "wan2.2_t2v_high_noise_14B_fp16.safetensors", "name": "Comfy-Org/Wan2.2 t2v high noise 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged", "save_path": "diffusion_models/Wan2.2", "size": "28.6GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged/resolve/main/split_files/diffusion_models/wan2.2_t2v_high_noise_14B_fp16.safetensors"}, {"base": "Wan2.2", "description": "Wan2.2 diffusion model for t2v high noise 14B (fp8_scaled)", "filename": "wan2.2_t2v_high_noise_14B_fp8_scaled.safetensors", "name": "Comfy-Org/Wan2.2 t2v high noise 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged", "save_path": "diffusion_models/Wan2.2", "size": "14.3GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged/resolve/main/split_files/diffusion_models/wan2.2_t2v_high_noise_14B_fp8_scaled.safetensors"}, {"base": "Wan2.2", "description": "Wan2.2 diffusion model for t2v low noise 14B (fp16)", "filename": "wan2.2_t2v_low_noise_14B_fp16.safetensors", "name": "Comfy-Org/Wan2.2 t2v low noise 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged", "save_path": "diffusion_models/Wan2.2", "size": "28.6GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged/resolve/main/split_files/diffusion_models/wan2.2_t2v_low_noise_14B_fp16.safetensors"}, {"base": "Wan2.2", "description": "Wan2.2 diffusion model for t2v low noise 14B (fp8_scaled)", "filename": "wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors", "name": "Comfy-Org/Wan2.2 t2v low noise 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged", "save_path": "diffusion_models/Wan2.2", "size": "14.3GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged/resolve/main/split_files/diffusion_models/wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors"}, {"base": "Wan2.2", "description": "Wan2.2 diffusion model for ti2v 5B (fp16)", "filename": "wan2.2_ti2v_5B_fp16.safetensors", "name": "Comfy-Org/Wan2.2 ti2v 5B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged", "save_path": "diffusion_models/Wan2.2", "size": "10.0GB", "type": "diffusion_model", "url": "https://huggingface.co/Comfy-Org/Wan_2.2_ComfyUI_Repackaged/resolve/main/split_files/diffusion_models/wan2.2_ti2v_5B_fp16.safetensors"}, {"base": "umt5_xxl", "description": "umt5_xxl_fp16 text encoder for Wan2.1", "filename": "umt5_xxl_fp16.safetensors", "name": "Comfy-Org/umt5_xxl_fp16.safetensors", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "text_encoders", "size": "11.4GB", "type": "clip", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors"}, {"base": "umt5_xxl", "description": "umt5_xxl_fp8_e4m3fn_scaled text encoder for Wan2.1", "filename": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "name": "Comfy-Org/umt5_xxl_fp8_e4m3fn_scaled.safetensors", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "save_path": "text_encoders", "size": "6.74GB", "type": "clip", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors"}, {"base": "FramePackI2V", "description": "[SNAPSHOT] This is the f1k1_x_g9_f1k1f2k2f16k4_td FramePack for HY. [w/You cannot download this item on ComfyUI-Manager versions below V3.18]", "filename": "<huggingface>", "name": "lllyasviel/FramePackI2V_HY", "reference": "https://huggingface.co/lllyasviel/FramePackI2V_HY", "save_path": "diffusers/lllyasviel", "size": "25.75GB", "type": "FramePackI2V", "url": "lllyasviel/FramePackI2V_HY"}, {"base": "upscale", "description": "Spatial upscaler model for LTX-Video. This model enhances the spatial resolution of generated videos.", "filename": "ltxv-spatial-upscaler-0.9.7.safetensors", "name": "LTX-Video Spatial Upscaler v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "default", "size": "505MB", "type": "upscale", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-spatial-upscaler-0.9.7.safetensors"}, {"base": "upscale", "description": "Temporal upscaler model for LTX-Video. This model enhances the temporal resolution and smoothness of generated videos.", "filename": "ltxv-temporal-upscaler-0.9.7.safetensors", "name": "LTX-Video Temporal Upscaler v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "default", "size": "524MB", "type": "upscale", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-temporal-upscaler-0.9.7.safetensors"}, {"base": "LTX-Video", "description": "High-resolution quality LTX-Video 13B model.", "filename": "ltxv-13b-0.9.7-dev.safetensors", "name": "LTX-Video 13B v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "28.6GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-dev.safetensors"}, {"base": "LTX-Video", "description": "Quantized version of the LTX-Video 13B model, optimized for lower VRAM usage while maintaining high quality.", "filename": "ltxv-13b-0.9.7-dev-fp8.safetensors", "name": "LTX-Video 13B FP8 v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "15.7GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-dev-fp8.safetensors"}, {"base": "LTX-Video", "description": "Distilled version of the LTX-Video 13B model, providing improved efficiency while maintaining high-resolution quality.", "filename": "ltxv-13b-0.9.7-distilled.safetensors", "name": "LTX-Video 13B Distilled v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "28.6GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-distilled.safetensors"}, {"base": "LTX-Video", "description": "Quantized distilled version of the LTX-Video 13B model, optimized for even lower VRAM usage while maintaining quality.", "filename": "ltxv-13b-0.9.7-distilled-fp8.safetensors", "name": "LTX-Video 13B Distilled FP8 v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "15.7GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-distilled-fp8.safetensors"}, {"base": "LTX-Video", "description": "LTX-Video 2B distilled model v0.9.8 with improved prompt understanding and detail generation.", "filename": "ltxv-2b-0.9.8-distilled.safetensors", "name": "LTX-Video 2B Distilled v0.9.8", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "6.34GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-2b-0.9.8-distilled.safetensors"}, {"base": "LTX-Video", "description": "Quantized LTX-Video 2B distilled model v0.9.8 with improved prompt understanding and detail generation, optimized for lower VRAM usage.", "filename": "ltxv-2b-0.9.8-distilled-fp8.safetensors", "name": "LTX-Video 2B Distilled FP8 v0.9.8", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "4.46GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-2b-0.9.8-distilled-fp8.safetensors"}, {"base": "LTX-Video", "description": "LTX-Video 13B distilled model v0.9.8 with improved prompt understanding and detail generation.", "filename": "ltxv-13b-0.9.8-distilled.safetensors", "name": "LTX-Video 13B Distilled v0.9.8", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "28.6GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.8-distilled.safetensors"}, {"base": "LTX-Video", "description": "Quantized LTX-Video 13B distilled model v0.9.8 with improved prompt understanding and detail generation, optimized for lower VRAM usage.", "filename": "ltxv-13b-0.9.8-distilled-fp8.safetensors", "name": "LTX-Video 13B Distilled FP8 v0.9.8", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "checkpoints/LTXV", "size": "15.7GB", "type": "checkpoint", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.8-distilled-fp8.safetensors"}, {"base": "LTX-Video", "description": "A LoRA adapter that transforms the standard LTX-Video 13B model into a distilled version when loaded.", "filename": "ltxv-13b-0.9.7-distilled-lora128.safetensors", "name": "LTX-Video 13B Distilled LoRA v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video", "save_path": "loras", "size": "1.33GB", "type": "lora", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-distilled-lora128.safetensors"}, {"base": "LTX-Video", "description": "In-Context LoRA (IC LoRA) for depth-controlled video-to-video generation with precise depth conditioning.", "filename": "ltxv-097-ic-lora-depth-control-comfyui.safetensors", "name": "LTX-Video ICLoRA Depth 13B v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video-ICLoRA-depth-13b-0.9.7", "save_path": "loras", "size": "81.9MB", "type": "lora", "url": "https://huggingface.co/Lightricks/LTX-Video-ICLoRA-depth-13b-0.9.7/resolve/main/ltxv-097-ic-lora-depth-control-comfyui.safetensors"}, {"base": "LTX-Video", "description": "In-Context LoRA (IC LoRA) for pose-controlled video-to-video generation with precise pose conditioning.", "filename": "ltxv-097-ic-lora-pose-control-comfyui.safetensors", "name": "LTX-Video ICLoRA Pose 13B v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video-ICLoRA-pose-13b-0.9.7", "save_path": "loras", "size": "151MB", "type": "lora", "url": "https://huggingface.co/Lightricks/LTX-Video-ICLoRA-pose-13b-0.9.7/resolve/main/ltxv-097-ic-lora-pose-control-comfyui.safetensors"}, {"base": "LTX-Video", "description": "In-Context LoRA (IC LoRA) for canny edge-controlled video-to-video generation with precise edge conditioning.", "filename": "ltxv-097-ic-lora-canny-control-comfyui.safetensors", "name": "LTX-Video ICLoRA Canny 13B v0.9.7", "reference": "https://huggingface.co/Lightricks/LTX-Video-ICLoRA-canny-13b-0.9.7", "save_path": "loras", "size": "81.9MB", "type": "lora", "url": "https://huggingface.co/Lightricks/LTX-Video-ICLoRA-canny-13b-0.9.7/resolve/main/ltxv-097-ic-lora-canny-control-comfyui.safetensors"}, {"base": "LTX-Video", "description": "A video detailer model on top of LTXV_13B_098_DEV trained on custom data using In-Context LoRA (IC LoRA) method.", "filename": "ltxv-098-ic-lora-detailer-comfyui.safetensors", "name": "LTX-Video ICLoRA Detailer 13B v0.9.8", "reference": "https://huggingface.co/Lightricks/LTX-Video-ICLoRA-detailer-13b-0.9.8", "save_path": "loras", "size": "1.31GB", "type": "lora", "url": "https://huggingface.co/Lightricks/LTX-Video-ICLoRA-detailer-13b-0.9.8/resolve/main/ltxv-098-ic-lora-detailer-comfyui.safetensors"}, {"base": "LBM", "description": "Latent Bridge Matching (LBM) Relighting model", "filename": "LBM_relighting.safetensors", "name": "Latent Bridge Matching for Image Relighting", "reference": "https://huggingface.co/jasperai/LBM_relighting", "save_path": "diffusion_models/LBM", "size": "5.02GB", "type": "diffusion_model", "url": "https://huggingface.co/jasperai/LBM_relighting/resolve/main/model.safetensors"}]}