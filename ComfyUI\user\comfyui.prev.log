## ComfyUI-Manager: installing dependencies done.
[2025-08-31 20:02:01.433] ** ComfyUI startup time: 2025-08-31 20:02:01.433
[2025-08-31 20:02:01.434] ** Platform: Windows
[2025-08-31 20:02:01.434] ** Python version: 3.13.6 (tags/v3.13.6:4e66535, Aug  6 2025, 14:36:00) [MSC v.1944 64 bit (AMD64)]
[2025-08-31 20:02:01.434] ** Python executable: C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\python.exe
[2025-08-31 20:02:01.435] ** ComfyUI Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 20:02:01.435] ** ComfyUI Base Folder Path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI
[2025-08-31 20:02:01.436] ** User directory: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user
[2025-08-31 20:02:01.446] ** ComfyUI-Manager config path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-08-31 20:02:01.447] ** Log path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-08-31 20:02:02.308]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\rgthree-comfy
[2025-08-31 20:02:02.308]    2.2 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Manager-main
[2025-08-31 20:02:02.308] 
[2025-08-31 20:02:06.158] Checkpoint files will always be loaded safely.
[2025-08-31 20:02:08.686] Total VRAM 8151 MB, total RAM 32472 MB
[2025-08-31 20:02:08.686] pytorch version: 2.8.0+cu129
[2025-08-31 20:02:08.687] Set vram state to: NORMAL_VRAM
[2025-08-31 20:02:08.687] Device: cuda:0 NVIDIA GeForce RTX 5060 Laptop GPU : cudaMallocAsync
[2025-08-31 20:02:11.549] Using pytorch attention
[2025-08-31 20:02:18.801] Python version: 3.13.6 (tags/v3.13.6:4e66535, Aug  6 2025, 14:36:00) [MSC v.1944 64 bit (AMD64)]
[2025-08-31 20:02:18.802] ComfyUI version: 0.3.56
[2025-08-31 20:02:19.031] ComfyUI frontend version: 1.25.11
[2025-08-31 20:02:19.036] [Prompt Server] web root: C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-08-31 20:02:23.449] [Crystools [0;32mINFO[0m] Crystools version: 1.27.3
[2025-08-31 20:02:23.529] [Crystools [0;32mINFO[0m] Platform release: 11
[2025-08-31 20:02:23.529] [Crystools [0;32mINFO[0m] JETSON: Not detected.
[2025-08-31 20:02:23.532] [Crystools [0;32mINFO[0m] CPU: Intel(R) Core(TM) i7-14650HX - Arch: AMD64 - OS: Windows 11
[2025-08-31 20:02:23.581] [Crystools [0;32mINFO[0m] pynvml (NVIDIA) initialized.
[2025-08-31 20:02:23.582] [Crystools [0;32mINFO[0m] GPU/s:
[2025-08-31 20:02:23.593] [Crystools [0;32mINFO[0m] 0) NVIDIA GeForce RTX 5060 Laptop GPU
[2025-08-31 20:02:23.594] [Crystools [0;32mINFO[0m] NVIDIA Driver: 573.01
[2025-08-31 20:02:23.629] Total VRAM 8151 MB, total RAM 32472 MB
[2025-08-31 20:02:23.630] pytorch version: 2.8.0+cu129
[2025-08-31 20:02:23.632] Set vram state to: NORMAL_VRAM
[2025-08-31 20:02:23.632] Device: cuda:0 NVIDIA GeForce RTX 5060 Laptop GPU : cudaMallocAsync
[2025-08-31 20:02:23.790] ### Loading: ComfyUI-Manager (V3.36)
[2025-08-31 20:02:23.794] [ComfyUI-Manager] network_mode: public
[2025-08-31 20:02:23.879] ### ComfyUI Revision: 150 [4449e147] *DETACHED | Released on '2025-08-30'
[2025-08-31 20:02:24.834] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-08-31 20:02:24.839] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-08-31 20:02:24.855] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-08-31 20:02:24.958] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-08-31 20:02:25.038] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-08-31 20:02:30.788] FETCH ComfyRegistry Data: 5/96
[2025-08-31 20:02:32.926] ------------------------------------------
[2025-08-31 20:02:32.928] Comfyroll Studio v1.76 :  175 Nodes Loaded
[2025-08-31 20:02:32.928] ------------------------------------------
[2025-08-31 20:02:32.929] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-08-31 20:02:32.930] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-08-31 20:02:32.931] ------------------------------------------
[2025-08-31 20:02:32.949] [C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts
[2025-08-31 20:02:33.016] [C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False
[2025-08-31 20:02:33.022] [C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider']
[2025-08-31 20:02:33.096] C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-08-31 20:02:33.224] # 😺dzNodes: LayerStyle -> Cannot import name 'guidedFilter' from 'cv2.ximgproc'
A few nodes cannot works properly, while most nodes are not affected. Please REINSTALL package 'opencv-contrib-python'.
For detail refer to https://github.com/chflame163/ComfyUI_LayerStyle/issues/5
[2025-08-31 20:02:36.869] FETCH ComfyRegistry Data: 10/96
[2025-08-31 20:02:37.394] C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\timm\models\layers\__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
  warnings.warn(f"Importing from {__name__} is deprecated, please import via timm.layers", FutureWarning)
[2025-08-31 20:02:37.972] C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-08-31 20:02:38.560] # 😺dzNodes: WordCloud ->  find 345 fonts in C:\Windows\fonts
[2025-08-31 20:02:40.592] 
[2025-08-31 20:02:40.592] [rgthree-comfy] Loaded 48 exciting nodes. 🎉
[2025-08-31 20:02:40.592] 
[2025-08-31 20:02:41.546] WAS Node Suite: OpenCV Python FFMPEG support is enabled
[2025-08-31 20:02:41.548] WAS Node Suite Warning: `ffmpeg_bin_path` is not set in `C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-node-suite-comfyui\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.
[2025-08-31 20:02:41.929] WAS Node Suite: Finished. Loaded 220 nodes successfully.
[2025-08-31 20:02:41.930] 
	"Art is the most intense mode of individualism that the world has known." - Oscar Wilde
[2025-08-31 20:02:41.931] 
[2025-08-31 20:02:41.934] 
Import times for custom nodes:
[2025-08-31 20:02:41.934]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-08-31 20:02:41.934]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_AdvancedRefluxControl
[2025-08-31 20:02:41.934]    0.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_essentials
[2025-08-31 20:02:41.935]    0.1 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-ic-light
[2025-08-31 20:02:41.935]    0.1 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-08-31 20:02:41.935]    0.1 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Manager-main
[2025-08-31 20:02:41.935]    0.2 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-08-31 20:02:41.935]    0.6 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-Crystools
[2025-08-31 20:02:41.935]    0.7 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_wordcloud
[2025-08-31 20:02:41.935]    1.1 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-08-31 20:02:41.935]    1.3 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-node-suite-comfyui
[2025-08-31 20:02:41.935]    2.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\rgthree-comfy
[2025-08-31 20:02:41.935]    4.7 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle
[2025-08-31 20:02:41.935]    8.0 seconds: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_CatVTON_Wrapper
[2025-08-31 20:02:41.935] 
[2025-08-31 20:02:42.742] Context impl SQLiteImpl.
[2025-08-31 20:02:42.742] Will assume non-transactional DDL.
[2025-08-31 20:02:42.745] No target revision found.
[2025-08-31 20:02:42.823] Starting server

[2025-08-31 20:02:42.825] To see the GUI go to: http://127.0.0.1:8188
[2025-08-31 20:02:43.204] FETCH ComfyRegistry Data: 15/96
[2025-08-31 20:02:49.581] FETCH ComfyRegistry Data: 20/96
[2025-08-31 20:02:57.242] FETCH ComfyRegistry Data: 25/96
[2025-08-31 20:03:03.394] FETCH ComfyRegistry Data: 30/96
[2025-08-31 20:03:10.116] FETCH ComfyRegistry Data: 35/96
[2025-08-31 20:03:15.901] FETCH ComfyRegistry Data: 40/96
[2025-08-31 20:03:22.278] FETCH ComfyRegistry Data: 45/96
[2025-08-31 20:03:28.352] FETCH ComfyRegistry Data: 50/96
[2025-08-31 20:03:34.664] FETCH ComfyRegistry Data: 55/96
[2025-08-31 20:03:40.791] FETCH ComfyRegistry Data: 60/96
[2025-08-31 20:03:47.443] FETCH ComfyRegistry Data: 65/96
[2025-08-31 20:03:53.307] FETCH ComfyRegistry Data: 70/96
[2025-08-31 20:03:59.642] FETCH ComfyRegistry Data: 75/96
[2025-08-31 20:04:06.288] FETCH ComfyRegistry Data: 80/96
[2025-08-31 20:04:14.768] FETCH ComfyRegistry Data: 85/96
[2025-08-31 20:04:26.183] FETCH ComfyRegistry Data: 90/96
[2025-08-31 20:04:34.464] FETCH ComfyRegistry Data: 95/96
[2025-08-31 20:04:35.958] FETCH ComfyRegistry Data [DONE]
[2025-08-31 20:04:36.651] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-08-31 20:04:36.753] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-08-31 20:04:37.631] [ComfyUI-Manager] All startup tasks have been completed.
[2025-08-31 20:04:59.076] got prompt
[2025-08-31 20:05:18.503] final text_encoder_type: bert-base-uncased
[2025-08-31 20:05:25.827] C:\Users\<USER>\Documents\ComfyUI_windows_portable\python_embeded\Lib\site-packages\transformers\modeling_utils.py:1773: FutureWarning: The `device` argument is deprecated and will be removed in v5 of Transformers.
  warnings.warn(
[2025-08-31 20:05:28.174] C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\local_groundingdino\models\GroundingDINO\transformer.py:862: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with torch.cuda.amp.autocast(enabled=False):
[2025-08-31 20:05:40.267] # 😺dzNodes: LayerStyle -> SegmentAnythingUltra V2 Processed 1 image(s).
[2025-08-31 20:05:40.402] # 😺dzNodes: LayerStyle -> lama copy
[2025-08-31 20:05:40.445] # 😺dzNodes: LayerStyle -> config file written: C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\temp\_lama_4my5yona8zz9yc9n_temp\config.json
[2025-08-31 20:05:43.292] !!! Exception during processing !!! No module named 'imghdr'
[2025-08-31 20:05:43.301] Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 496, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 315, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb, hidden_inputs=hidden_inputs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 289, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\execution.py", line 277, in process_inputs
    result = f(**inputs)
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\lama.py", line 111, in lama
    cli.run(model=lama_model, device=device, image=Path(image_dir), mask=Path(mask_dir), output=Path(result_dir), config=Path(config_dir))
    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\cli.py", line 81, in run
    from .download import cli_download_model, scan_models
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\download.py", line 18, in <module>
    from .model.original_sd_configs import get_config_files
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\__init__.py", line 1, in <module>
    from .anytext.anytext_model import AnyText
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\anytext_model.py", line 5, in <module>
    from ...model.anytext.anytext_pipeline import AnyTextPipeline
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\anytext_pipeline.py", line 20, in <module>
    from ...model.anytext.cldm.model import create_model, load_state_dict
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\cldm\model.py", line 5, in <module>
    from iopaint.model.anytext.ldm.util import instantiate_from_config
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\__init__.py", line 1, in <module>
    from .anytext.anytext_model import AnyText
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\anytext\anytext_model.py", line 6, in <module>
    from ...model.base import DiffusionInpaintModel
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\model\base.py", line 9, in <module>
    from ..helper import (
    ...<4 lines>...
    )
  File "C:\Users\<USER>\Documents\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_layerstyle\py\iopaint\helper.py", line 2, in <module>
    import imghdr
ModuleNotFoundError: No module named 'imghdr'

[2025-08-31 20:05:43.314] Prompt executed in 44.23 seconds
[2025-08-31 20:42:31.278] 
Stopped server
